<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /var/www/Nzoom-Hella/_libs/Nzoom/Export/Adapter</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../_css/bootstrap.min.css?v=9.2.32" rel="stylesheet" type="text/css">
  <link href="../_css/nv.d3.min.css?v=9.2.32" rel="stylesheet" type="text/css">
  <link href="../_css/style.css?v=9.2.32" rel="stylesheet" type="text/css">
  <link href="../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../index.html">/var/www/Nzoom-Hella/_libs/Nzoom/Export</a></li>
         <li class="breadcrumb-item"><a href="index.html">Adapter</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="AbstractExportFormatAdapter.php.html#100"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::sendHeaders">sendHeaders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#839"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::createTableWorksheet">createTableWorksheet</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#867"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::sanitizeWorksheetName">sanitizeWorksheetName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#881"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::populateTableWorksheet">populateTableWorksheet</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#915"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::addTableDataToWorksheet">addTableDataToWorksheet</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#193"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::getApplicationLocale">getApplicationLocale</abbr></a></td><td class="text-right">11%</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#785"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::processExportTables">processExportTables</abbr></a></td><td class="text-right">28%</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#672"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::applyColumnWidthConstraints">applyColumnWidthConstraints</abbr></a></td><td class="text-right">38%</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#809"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::collectTablesByType">collectTablesByType</abbr></a></td><td class="text-right">45%</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#170"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::setSpreadsheetLocale">setSpreadsheetLocale</abbr></a></td><td class="text-right">50%</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#497"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::setCellDateValue">setCellDateValue</abbr></a></td><td class="text-right">60%</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#106"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::extractSizingOptions">extractSizingOptions</abbr></a></td><td class="text-right">63%</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#310"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::addNamedRanges">addNamedRanges</abbr></a></td><td class="text-right">68%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#839"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::createTableWorksheet">createTableWorksheet</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#106"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::extractSizingOptions">extractSizingOptions</abbr></a></td><td class="text-right">16</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#193"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::getApplicationLocale">getApplicationLocale</abbr></a></td><td class="text-right">15</td></tr>
       <tr><td><a href="AbstractExportFormatAdapter.php.html#100"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::sendHeaders">sendHeaders</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#881"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::populateTableWorksheet">populateTableWorksheet</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#672"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::applyColumnWidthConstraints">applyColumnWidthConstraints</abbr></a></td><td class="text-right">10</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#785"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::processExportTables">processExportTables</abbr></a></td><td class="text-right">9</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#809"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::collectTablesByType">collectTablesByType</abbr></a></td><td class="text-right">9</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#497"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::setCellDateValue">setCellDateValue</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#170"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::setSpreadsheetLocale">setSpreadsheetLocale</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#915"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::addTableDataToWorksheet">addTableDataToWorksheet</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#310"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::addNamedRanges">addNamedRanges</abbr></a></td><td class="text-right">4</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 9.2.32</a> using <a href="https://secure.php.net/" target="_top">PHP 7.4.33</a> and <a href="https://phpunit.de/">PHPUnit 9.6.23</a> at Fri Jun 20 10:03:39 UTC 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../_js/jquery.min.js?v=9.2.32" type="text/javascript"></script>
  <script src="../_js/d3.min.js?v=9.2.32" type="text/javascript"></script>
  <script src="../_js/nv.d3.min.js?v=9.2.32" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([0,0,0,0,0,0,0,0,1,1,2,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([5,0,1,1,1,1,1,3,4,6,13,54], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[83.69565217391305,38,"<a href=\"AbstractExportFormatAdapter.php.html#12\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter<\/a>"],[95.83333333333334,79,"<a href=\"CsvExportFormatAdapter.php.html#15\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter<\/a>"],[77.64705882352942,233,"<a href=\"ExcelExportFormatAdapter.php.html#27\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter<\/a>"],[97.48743718592965,71,"<a href=\"JsonExportFormatAdapter.php.html#15\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[100,2,"<a href=\"AbstractExportFormatAdapter.php.html#43\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::__construct<\/a>"],[100,1,"<a href=\"AbstractExportFormatAdapter.php.html#57\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::setConfiguration<\/a>"],[100,5,"<a href=\"AbstractExportFormatAdapter.php.html#70\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::getExportFilename<\/a>"],[0,3,"<a href=\"AbstractExportFormatAdapter.php.html#100\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::sendHeaders<\/a>"],[85.71428571428571,4,"<a href=\"AbstractExportFormatAdapter.php.html#127\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::handleExportError<\/a>"],[100,5,"<a href=\"AbstractExportFormatAdapter.php.html#161\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::getRecordHeaders<\/a>"],[100,1,"<a href=\"AbstractExportFormatAdapter.php.html#181\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::getFormatOptions<\/a>"],[100,3,"<a href=\"AbstractExportFormatAdapter.php.html#193\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::validateAndPrepareSaveTarget<\/a>"],[100,2,"<a href=\"AbstractExportFormatAdapter.php.html#213\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::validateStringTarget<\/a>"],[94.11764705882352,5,"<a href=\"AbstractExportFormatAdapter.php.html#231\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::validatePhpStreamWrapper<\/a>"],[83.33333333333334,3,"<a href=\"AbstractExportFormatAdapter.php.html#273\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::validateFilePath<\/a>"],[90,4,"<a href=\"AbstractExportFormatAdapter.php.html#295\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::validateFilePointer<\/a>"],[100,4,"<a href=\"CsvExportFormatAdapter.php.html#50\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::export<\/a>"],[90.47619047619048,19,"<a href=\"CsvExportFormatAdapter.php.html#85\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::extractCsvOptions<\/a>"],[93.33333333333333,6,"<a href=\"CsvExportFormatAdapter.php.html#128\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::writeCsvContent<\/a>"],[100,7,"<a href=\"CsvExportFormatAdapter.php.html#173\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::processExportDataRecords<\/a>"],[100,2,"<a href=\"CsvExportFormatAdapter.php.html#209\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::processExportRecord<\/a>"],[91.66666666666666,12,"<a href=\"CsvExportFormatAdapter.php.html#227\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::formatValueForCsv<\/a>"],[87.5,7,"<a href=\"CsvExportFormatAdapter.php.html#271\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::formatDateValue<\/a>"],[100,2,"<a href=\"CsvExportFormatAdapter.php.html#296\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::getDecimalPlaces<\/a>"],[75,4,"<a href=\"CsvExportFormatAdapter.php.html#312\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::getDelimiter<\/a>"],[100,6,"<a href=\"CsvExportFormatAdapter.php.html#339\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::normalizeDelimiter<\/a>"],[100,1,"<a href=\"CsvExportFormatAdapter.php.html#360\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::setDateFormat<\/a>"],[100,1,"<a href=\"CsvExportFormatAdapter.php.html#371\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::setDatetimeFormat<\/a>"],[100,1,"<a href=\"CsvExportFormatAdapter.php.html#381\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::getDateFormat<\/a>"],[100,1,"<a href=\"CsvExportFormatAdapter.php.html#391\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::getDatetimeFormat<\/a>"],[100,1,"<a href=\"CsvExportFormatAdapter.php.html#399\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::getSupportedExtensions<\/a>"],[100,1,"<a href=\"CsvExportFormatAdapter.php.html#407\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::getMimeType<\/a>"],[100,1,"<a href=\"CsvExportFormatAdapter.php.html#416\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::getDefaultExtension<\/a>"],[100,1,"<a href=\"CsvExportFormatAdapter.php.html#424\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::supportsFormat<\/a>"],[100,1,"<a href=\"CsvExportFormatAdapter.php.html#432\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::getFormatName<\/a>"],[100,1,"<a href=\"CsvExportFormatAdapter.php.html#440\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::getFormatOptions<\/a>"],[93.75,5,"<a href=\"ExcelExportFormatAdapter.php.html#56\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::export<\/a>"],[63.63636363636363,11,"<a href=\"ExcelExportFormatAdapter.php.html#106\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::extractSizingOptions<\/a>"],[100,1,"<a href=\"ExcelExportFormatAdapter.php.html#138\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::createSpreadsheet<\/a>"],[50,4,"<a href=\"ExcelExportFormatAdapter.php.html#170\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::setSpreadsheetLocale<\/a>"],[11.11111111111111,4,"<a href=\"ExcelExportFormatAdapter.php.html#193\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::getApplicationLocale<\/a>"],[87.5,2,"<a href=\"ExcelExportFormatAdapter.php.html#244\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::setDocumentProperties<\/a>"],[100,1,"<a href=\"ExcelExportFormatAdapter.php.html#265\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::processExportData<\/a>"],[100,2,"<a href=\"ExcelExportFormatAdapter.php.html#295\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::addHeaders<\/a>"],[68.75,4,"<a href=\"ExcelExportFormatAdapter.php.html#310\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::addNamedRanges<\/a>"],[100,1,"<a href=\"ExcelExportFormatAdapter.php.html#346\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::styleHeaderRow<\/a>"],[70.58823529411765,8,"<a href=\"ExcelExportFormatAdapter.php.html#362\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::processExportDataRecords<\/a>"],[71.42857142857143,3,"<a href=\"ExcelExportFormatAdapter.php.html#411\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::processExportRecord<\/a>"],[80,17,"<a href=\"ExcelExportFormatAdapter.php.html#436\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::setCellValueWithFormatting<\/a>"],[60,5,"<a href=\"ExcelExportFormatAdapter.php.html#497\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::setCellDateValue<\/a>"],[92,15,"<a href=\"ExcelExportFormatAdapter.php.html#525\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::getExcelFormatFromExportValue<\/a>"],[100,3,"<a href=\"ExcelExportFormatAdapter.php.html#576\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::convertCustomNumberFormat<\/a>"],[100,1,"<a href=\"ExcelExportFormatAdapter.php.html#599\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::convertCustomDateFormat<\/a>"],[100,2,"<a href=\"ExcelExportFormatAdapter.php.html#630\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::handleExportRecordCellError<\/a>"],[100,2,"<a href=\"ExcelExportFormatAdapter.php.html#648\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::finalizeExportDataColumns<\/a>"],[38.46153846153847,5,"<a href=\"ExcelExportFormatAdapter.php.html#672\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::applyColumnWidthConstraints<\/a>"],[76,8,"<a href=\"ExcelExportFormatAdapter.php.html#709\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::applyRowHeightConstraints<\/a>"],[100,3,"<a href=\"ExcelExportFormatAdapter.php.html#766\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::applyVerticalAlignment<\/a>"],[28.57142857142857,4,"<a href=\"ExcelExportFormatAdapter.php.html#785\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::processExportTables<\/a>"],[45.45454545454545,5,"<a href=\"ExcelExportFormatAdapter.php.html#809\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::collectTablesByType<\/a>"],[0,4,"<a href=\"ExcelExportFormatAdapter.php.html#839\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::createTableWorksheet<\/a>"],[0,1,"<a href=\"ExcelExportFormatAdapter.php.html#867\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::sanitizeWorksheetName<\/a>"],[0,3,"<a href=\"ExcelExportFormatAdapter.php.html#881\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::populateTableWorksheet<\/a>"],[0,2,"<a href=\"ExcelExportFormatAdapter.php.html#915\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::addTableDataToWorksheet<\/a>"],[100,4,"<a href=\"ExcelExportFormatAdapter.php.html#935\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::createWriter<\/a>"],[90,4,"<a href=\"ExcelExportFormatAdapter.php.html#956\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::handleSpreadsheetError<\/a>"],[100,83,"<a href=\"ExcelExportFormatAdapter.php.html#986\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::getExcelFormatting<\/a>"],[100,3,"<a href=\"ExcelExportFormatAdapter.php.html#1088\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::optimizeMemoryForExport<\/a>"],[90,4,"<a href=\"ExcelExportFormatAdapter.php.html#1111\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::convertToBytes<\/a>"],[100,1,"<a href=\"ExcelExportFormatAdapter.php.html#1134\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::getSupportedExtensions<\/a>"],[100,4,"<a href=\"ExcelExportFormatAdapter.php.html#1142\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::getMimeType<\/a>"],[100,1,"<a href=\"ExcelExportFormatAdapter.php.html#1159\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::getDefaultExtension<\/a>"],[100,1,"<a href=\"ExcelExportFormatAdapter.php.html#1167\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::supportsFormat<\/a>"],[100,1,"<a href=\"ExcelExportFormatAdapter.php.html#1175\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::getFormatName<\/a>"],[100,1,"<a href=\"ExcelExportFormatAdapter.php.html#1183\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::getFormatOptions<\/a>"],[100,4,"<a href=\"JsonExportFormatAdapter.php.html#30\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::export<\/a>"],[100,7,"<a href=\"JsonExportFormatAdapter.php.html#65\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::extractJsonOptions<\/a>"],[93.33333333333333,6,"<a href=\"JsonExportFormatAdapter.php.html#89\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::writeJsonContent<\/a>"],[100,5,"<a href=\"JsonExportFormatAdapter.php.html#129\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::prepareJsonData<\/a>"],[90.9090909090909,5,"<a href=\"JsonExportFormatAdapter.php.html#148\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::prepareArrayStructure<\/a>"],[100,2,"<a href=\"JsonExportFormatAdapter.php.html#176\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::prepareObjectStructure<\/a>"],[100,6,"<a href=\"JsonExportFormatAdapter.php.html#201\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::prepareNestedStructure<\/a>"],[88.88888888888889,3,"<a href=\"JsonExportFormatAdapter.php.html#233\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::processExportRecord<\/a>"],[100,9,"<a href=\"JsonExportFormatAdapter.php.html#259\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::formatValueForJson<\/a>"],[90.9090909090909,7,"<a href=\"JsonExportFormatAdapter.php.html#297\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::formatDateValue<\/a>"],[100,1,"<a href=\"JsonExportFormatAdapter.php.html#324\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::addMetadata<\/a>"],[100,3,"<a href=\"JsonExportFormatAdapter.php.html#338\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::getMetadata<\/a>"],[96.29629629629629,7,"<a href=\"JsonExportFormatAdapter.php.html#373\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::getJsonOptions<\/a>"],[100,1,"<a href=\"JsonExportFormatAdapter.php.html#428\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::getSupportedExtensions<\/a>"],[100,1,"<a href=\"JsonExportFormatAdapter.php.html#436\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::getMimeType<\/a>"],[100,1,"<a href=\"JsonExportFormatAdapter.php.html#445\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::getDefaultExtension<\/a>"],[100,1,"<a href=\"JsonExportFormatAdapter.php.html#453\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::supportsFormat<\/a>"],[100,1,"<a href=\"JsonExportFormatAdapter.php.html#461\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::getFormatName<\/a>"],[100,1,"<a href=\"JsonExportFormatAdapter.php.html#469\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::getFormatOptions<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>
