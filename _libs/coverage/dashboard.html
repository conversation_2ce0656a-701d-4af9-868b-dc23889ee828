<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /var/www/Nzoom-Hella/_libs/Nzoom/Export</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="_css/bootstrap.min.css?v=9.2.32" rel="stylesheet" type="text/css">
  <link href="_css/nv.d3.min.css?v=9.2.32" rel="stylesheet" type="text/css">
  <link href="_css/style.css?v=9.2.32" rel="stylesheet" type="text/css">
  <link href="_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="index.html">/var/www/Nzoom-Hella/_libs/Nzoom/Export</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Provider/ModelTableProvider.php.html#18">Nzoom\Export\Provider\ModelTableProvider</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportTableCollection.php.html#11">Nzoom\Export\Entity\ExportTableCollection</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/GeneratorFileStreamer.php.html#11">Nzoom\Export\Streamer\GeneratorFileStreamer</a></td><td class="text-right">20%</td></tr>
       <tr><td><a href="Entity/ExportTable.php.html#11">Nzoom\Export\Entity\ExportTable</a></td><td class="text-right">21%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Provider/ModelTableProvider.php.html#18">Nzoom\Export\Provider\ModelTableProvider</a></td><td class="text-right">3540</td></tr>
       <tr><td><a href="Entity/ExportTableCollection.php.html#11">Nzoom\Export\Entity\ExportTableCollection</a></td><td class="text-right">1056</td></tr>
       <tr><td><a href="Entity/ExportTable.php.html#11">Nzoom\Export\Entity\ExportTable</a></td><td class="text-right">472</td></tr>
       <tr><td><a href="Streamer/GeneratorFileStreamer.php.html#11">Nzoom\Export\Streamer\GeneratorFileStreamer</a></td><td class="text-right">220</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Adapter/AbstractExportFormatAdapter.php.html#100"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::sendHeaders">sendHeaders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportTableCollection.php.html#190"><abbr title="Nzoom\Export\Entity\ExportTableCollection::setMetadataValue">setMetadataValue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Provider/ModelTableProvider.php.html#47"><abbr title="Nzoom\Export\Provider\ModelTableProvider::getDefaultConfig">getDefaultConfig</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Provider/ModelTableProvider.php.html#36"><abbr title="Nzoom\Export\Provider\ModelTableProvider::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportTableCollection.php.html#274"><abbr title="Nzoom\Export\Entity\ExportTableCollection::fromArray">fromArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportTableCollection.php.html#252"><abbr title="Nzoom\Export\Entity\ExportTableCollection::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportTableCollection.php.html#240"><abbr title="Nzoom\Export\Entity\ExportTableCollection::getIterator">getIterator</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportTableCollection.php.html#230"><abbr title="Nzoom\Export\Entity\ExportTableCollection::count">count</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportTableCollection.php.html#216"><abbr title="Nzoom\Export\Entity\ExportTableCollection::getTotalRecordCount">getTotalRecordCount</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportTableCollection.php.html#200"><abbr title="Nzoom\Export\Entity\ExportTableCollection::validate">validate</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportTableCollection.php.html#179"><abbr title="Nzoom\Export\Entity\ExportTableCollection::getMetadataValue">getMetadataValue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Provider/ModelTableProvider.php.html#97"><abbr title="Nzoom\Export\Provider\ModelTableProvider::extractTable">extractTable</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportTableCollection.php.html#167"><abbr title="Nzoom\Export\Entity\ExportTableCollection::setMetadata">setMetadata</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportTableCollection.php.html#157"><abbr title="Nzoom\Export\Entity\ExportTableCollection::getMetadata">getMetadata</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportTableCollection.php.html#146"><abbr title="Nzoom\Export\Entity\ExportTableCollection::getTableTypesWithRecords">getTableTypesWithRecords</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportTableCollection.php.html#134"><abbr title="Nzoom\Export\Entity\ExportTableCollection::getTablesWithRecords">getTablesWithRecords</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportTableCollection.php.html#124"><abbr title="Nzoom\Export\Entity\ExportTableCollection::hasTables">hasTables</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportTableCollection.php.html#114"><abbr title="Nzoom\Export\Entity\ExportTableCollection::clearTables">clearTables</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportTableCollection.php.html#106"><abbr title="Nzoom\Export\Entity\ExportTableCollection::getTableTypes">getTableTypes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportTableCollection.php.html#96"><abbr title="Nzoom\Export\Entity\ExportTableCollection::getTables">getTables</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportTableCollection.php.html#81"><abbr title="Nzoom\Export\Entity\ExportTableCollection::removeTable">removeTable</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Provider/ModelTableProvider.php.html#61"><abbr title="Nzoom\Export\Provider\ModelTableProvider::getTablesForRecord">getTablesForRecord</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Provider/ModelTableProvider.php.html#148"><abbr title="Nzoom\Export\Provider\ModelTableProvider::createTableHeader">createTableHeader</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportTableCollection.php.html#39"><abbr title="Nzoom\Export\Entity\ExportTableCollection::addTable">addTable</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#278"><abbr title="Nzoom\Export\Streamer\FileStreamer::getFilename">getFilename</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/PointerFileStreamer.php.html#136"><abbr title="Nzoom\Export\Streamer\PointerFileStreamer::getTotalSize">getTotalSize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/PointerFileStreamer.php.html#121"><abbr title="Nzoom\Export\Streamer\PointerFileStreamer::setChunkSize">setChunkSize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/PointerFileStreamer.php.html#110"><abbr title="Nzoom\Export\Streamer\PointerFileStreamer::getChunkSize">getChunkSize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/GeneratorFileStreamer.php.html#150"><abbr title="Nzoom\Export\Streamer\GeneratorFileStreamer::reset">reset</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/GeneratorFileStreamer.php.html#140"><abbr title="Nzoom\Export\Streamer\GeneratorFileStreamer::getTotalSize">getTotalSize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/GeneratorFileStreamer.php.html#123"><abbr title="Nzoom\Export\Streamer\GeneratorFileStreamer::setTotalSize">setTotalSize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/GeneratorFileStreamer.php.html#98"><abbr title="Nzoom\Export\Streamer\GeneratorFileStreamer::cleanup">cleanup</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/GeneratorFileStreamer.php.html#68"><abbr title="Nzoom\Export\Streamer\GeneratorFileStreamer::performStreaming">performStreaming</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#288"><abbr title="Nzoom\Export\Streamer\FileStreamer::getMimeType">getMimeType</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#107"><abbr title="Nzoom\Export\Streamer\FileStreamer::setCacheExpires">setCacheExpires</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Provider/ModelTableProvider.php.html#182"><abbr title="Nzoom\Export\Provider\ModelTableProvider::populateTableFromData">populateTableFromData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#96"><abbr title="Nzoom\Export\Streamer\FileStreamer::setTimeIncrement">setTimeIncrement</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#85"><abbr title="Nzoom\Export\Streamer\FileStreamer::setHeaders">setHeaders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Provider/ModelTableProvider.php.html#331"><abbr title="Nzoom\Export\Provider\ModelTableProvider::validateRecord">validateRecord</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Provider/ModelTableProvider.php.html#323"><abbr title="Nzoom\Export\Provider\ModelTableProvider::getTableConfiguration">getTableConfiguration</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Provider/ModelTableProvider.php.html#315"><abbr title="Nzoom\Export\Provider\ModelTableProvider::supportsTableType">supportsTableType</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Provider/ModelTableProvider.php.html#307"><abbr title="Nzoom\Export\Provider\ModelTableProvider::getSupportedTableTypes">getSupportedTableTypes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Provider/ModelTableProvider.php.html#268"><abbr title="Nzoom\Export\Provider\ModelTableProvider::formatValue">formatValue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Provider/ModelTableProvider.php.html#246"><abbr title="Nzoom\Export\Provider\ModelTableProvider::extractValueFromItem">extractValueFromItem</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Provider/ModelTableProvider.php.html#216"><abbr title="Nzoom\Export\Provider\ModelTableProvider::createRecordFromItem">createRecordFromItem</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportTableCollection.php.html#70"><abbr title="Nzoom\Export\Entity\ExportTableCollection::hasTable">hasTable</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportTableCollection.php.html#59"><abbr title="Nzoom\Export\Entity\ExportTableCollection::getTable">getTable</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportTableCollection.php.html#28"><abbr title="Nzoom\Export\Entity\ExportTableCollection::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DataFactory.php.html#96"><abbr title="Nzoom\Export\DataFactory::withModelTableProvider">withModelTableProvider</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportData.php.html#475"><abbr title="Nzoom\Export\Entity\ExportData::setTableConfigValue">setTableConfigValue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportTable.php.html#296"><abbr title="Nzoom\Export\Entity\ExportTable::getIterator">getIterator</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportData.php.html#452"><abbr title="Nzoom\Export\Entity\ExportData::setTableConfig">setTableConfig</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportData.php.html#442"><abbr title="Nzoom\Export\Entity\ExportData::getTableConfig">getTableConfig</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportData.php.html#421"><abbr title="Nzoom\Export\Entity\ExportData::disableTables">disableTables</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportData.php.html#412"><abbr title="Nzoom\Export\Entity\ExportData::enableTables">enableTables</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DataFactory.php.html#248"><abbr title="Nzoom\Export\DataFactory::extractTablesForRecord">extractTablesForRecord</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DataFactory.php.html#111"><abbr title="Nzoom\Export\DataFactory::createWithTables">createWithTables</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DataFactory.php.html#74"><abbr title="Nzoom\Export\DataFactory::disableTables">disableTables</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportRecord.php.html#245"><abbr title="Nzoom\Export\Entity\ExportRecord::setTableCollection">setTableCollection</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DataFactory.php.html#66"><abbr title="Nzoom\Export\DataFactory::enableTables">enableTables</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DataFactory.php.html#55"><abbr title="Nzoom\Export\DataFactory::setTableProvider">setTableProvider</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#917"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::addTableDataToWorksheet">addTableDataToWorksheet</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#883"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::populateTableWorksheet">populateTableWorksheet</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#869"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::sanitizeWorksheetName">sanitizeWorksheetName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#841"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::createTableWorksheet">createTableWorksheet</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#811"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::collectTablesByType">collectTablesByType</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#787"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::processExportTables">processExportTables</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportRecord.php.html#235"><abbr title="Nzoom\Export\Entity\ExportRecord::getTableCollection">getTableCollection</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportData.php.html#464"><abbr title="Nzoom\Export\Entity\ExportData::getTableConfigValue">getTableConfigValue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportRecord.php.html#255"><abbr title="Nzoom\Export\Entity\ExportRecord::hasTables">hasTables</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportTable.php.html#157"><abbr title="Nzoom\Export\Entity\ExportTable::getRecords">getRecords</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportTable.php.html#286"><abbr title="Nzoom\Export\Entity\ExportTable::count">count</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportTable.php.html#266"><abbr title="Nzoom\Export\Entity\ExportTable::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportTable.php.html#248"><abbr title="Nzoom\Export\Entity\ExportTable::validate">validate</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportTable.php.html#215"><abbr title="Nzoom\Export\Entity\ExportTable::setMetadata">setMetadata</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportRecord.php.html#266"><abbr title="Nzoom\Export\Entity\ExportRecord::getTable">getTable</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportTable.php.html#185"><abbr title="Nzoom\Export\Entity\ExportTable::clearRecords">clearRecords</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportTable.php.html#169"><abbr title="Nzoom\Export\Entity\ExportTable::setRecords">setRecords</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportTable.php.html#195"><abbr title="Nzoom\Export\Entity\ExportTable::hasRecords">hasRecords</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportTable.php.html#143"><abbr title="Nzoom\Export\Entity\ExportTable::addRecord">addRecord</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportTable.php.html#91"><abbr title="Nzoom\Export\Entity\ExportTable::setTableName">setTableName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportRecord.php.html#276"><abbr title="Nzoom\Export\Entity\ExportRecord::addTable">addTable</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportTable.php.html#131"><abbr title="Nzoom\Export\Entity\ExportTable::setParentReference">setParentReference</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportTable.php.html#81"><abbr title="Nzoom\Export\Entity\ExportTable::getTableName">getTableName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportTable.php.html#71"><abbr title="Nzoom\Export\Entity\ExportTable::getTableType">getTableType</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportTable.php.html#101"><abbr title="Nzoom\Export\Entity\ExportTable::getHeader">getHeader</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportTable.php.html#111"><abbr title="Nzoom\Export\Entity\ExportTable::setHeader">setHeader</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportTable.php.html#121"><abbr title="Nzoom\Export\Entity\ExportTable::getParentReference">getParentReference</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/StreamHeaders.php.html#189"><abbr title="Nzoom\Export\Streamer\StreamHeaders::flushHeaders">flushHeaders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#195"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::getApplicationLocale">getApplicationLocale</abbr></a></td><td class="text-right">11%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#674"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::applyColumnWidthConstraints">applyColumnWidthConstraints</abbr></a></td><td class="text-right">38%</td></tr>
       <tr><td><a href="Streamer/StreamHeaders.php.html#168"><abbr title="Nzoom\Export\Streamer\StreamHeaders::sendPreparedHeaders">sendPreparedHeaders</abbr></a></td><td class="text-right">40%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#172"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::setSpreadsheetLocale">setSpreadsheetLocale</abbr></a></td><td class="text-right">50%</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#200"><abbr title="Nzoom\Export\Streamer\FileStreamer::setStreamingOptimizationHeaders">setStreamingOptimizationHeaders</abbr></a></td><td class="text-right">60%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#499"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::setCellDateValue">setCellDateValue</abbr></a></td><td class="text-right">60%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#106"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::extractSizingOptions">extractSizingOptions</abbr></a></td><td class="text-right">63%</td></tr>
       <tr><td><a href="Streamer/GeneratorFileStreamer.php.html#56"><abbr title="Nzoom\Export\Streamer\GeneratorFileStreamer::initializeGenerator">initializeGenerator</abbr></a></td><td class="text-right">66%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#312"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::addNamedRanges">addNamedRanges</abbr></a></td><td class="text-right">68%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Provider/ModelTableProvider.php.html#268"><abbr title="Nzoom\Export\Provider\ModelTableProvider::formatValue">formatValue</abbr></a></td><td class="text-right">182</td></tr>
       <tr><td><a href="Provider/ModelTableProvider.php.html#61"><abbr title="Nzoom\Export\Provider\ModelTableProvider::getTablesForRecord">getTablesForRecord</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Streamer/GeneratorFileStreamer.php.html#68"><abbr title="Nzoom\Export\Streamer\GeneratorFileStreamer::performStreaming">performStreaming</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Provider/ModelTableProvider.php.html#182"><abbr title="Nzoom\Export\Provider\ModelTableProvider::populateTableFromData">populateTableFromData</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Provider/ModelTableProvider.php.html#97"><abbr title="Nzoom\Export\Provider\ModelTableProvider::extractTable">extractTable</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Provider/ModelTableProvider.php.html#331"><abbr title="Nzoom\Export\Provider\ModelTableProvider::validateRecord">validateRecord</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Entity/ExportTableCollection.php.html#274"><abbr title="Nzoom\Export\Entity\ExportTableCollection::fromArray">fromArray</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#811"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::collectTablesByType">collectTablesByType</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="DataFactory.php.html#248"><abbr title="Nzoom\Export\DataFactory::extractTablesForRecord">extractTablesForRecord</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Provider/ModelTableProvider.php.html#246"><abbr title="Nzoom\Export\Provider\ModelTableProvider::extractValueFromItem">extractValueFromItem</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#841"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::createTableWorksheet">createTableWorksheet</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Streamer/GeneratorFileStreamer.php.html#98"><abbr title="Nzoom\Export\Streamer\GeneratorFileStreamer::cleanup">cleanup</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Provider/ModelTableProvider.php.html#148"><abbr title="Nzoom\Export\Provider\ModelTableProvider::createTableHeader">createTableHeader</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#787"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::processExportTables">processExportTables</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Entity/ExportTable.php.html#169"><abbr title="Nzoom\Export\Entity\ExportTable::setRecords">setRecords</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#106"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::extractSizingOptions">extractSizingOptions</abbr></a></td><td class="text-right">16</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#195"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::getApplicationLocale">getApplicationLocale</abbr></a></td><td class="text-right">15</td></tr>
       <tr><td><a href="Adapter/AbstractExportFormatAdapter.php.html#100"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::sendHeaders">sendHeaders</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Entity/ExportTableCollection.php.html#200"><abbr title="Nzoom\Export\Entity\ExportTableCollection::validate">validate</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Provider/ModelTableProvider.php.html#216"><abbr title="Nzoom\Export\Provider\ModelTableProvider::createRecordFromItem">createRecordFromItem</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Entity/ExportTable.php.html#143"><abbr title="Nzoom\Export\Entity\ExportTable::addRecord">addRecord</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Entity/ExportTable.php.html#266"><abbr title="Nzoom\Export\Entity\ExportTable::toArray">toArray</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Entity/ExportTable.php.html#248"><abbr title="Nzoom\Export\Entity\ExportTable::validate">validate</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#883"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::populateTableWorksheet">populateTableWorksheet</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#674"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::applyColumnWidthConstraints">applyColumnWidthConstraints</abbr></a></td><td class="text-right">10</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#499"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::setCellDateValue">setCellDateValue</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Streamer/GeneratorFileStreamer.php.html#123"><abbr title="Nzoom\Export\Streamer\GeneratorFileStreamer::setTotalSize">setTotalSize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Streamer/PointerFileStreamer.php.html#121"><abbr title="Nzoom\Export\Streamer\PointerFileStreamer::setChunkSize">setChunkSize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Entity/ExportTableCollection.php.html#39"><abbr title="Nzoom\Export\Entity\ExportTableCollection::addTable">addTable</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Entity/ExportTableCollection.php.html#252"><abbr title="Nzoom\Export\Entity\ExportTableCollection::toArray">toArray</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Entity/ExportTableCollection.php.html#216"><abbr title="Nzoom\Export\Entity\ExportTableCollection::getTotalRecordCount">getTotalRecordCount</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Entity/ExportTableCollection.php.html#81"><abbr title="Nzoom\Export\Entity\ExportTableCollection::removeTable">removeTable</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Entity/ExportRecord.php.html#276"><abbr title="Nzoom\Export\Entity\ExportRecord::addTable">addTable</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Entity/ExportRecord.php.html#266"><abbr title="Nzoom\Export\Entity\ExportRecord::getTable">getTable</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Entity/ExportRecord.php.html#255"><abbr title="Nzoom\Export\Entity\ExportRecord::hasTables">hasTables</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="DataFactory.php.html#111"><abbr title="Nzoom\Export\DataFactory::createWithTables">createWithTables</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#917"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::addTableDataToWorksheet">addTableDataToWorksheet</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#172"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::setSpreadsheetLocale">setSpreadsheetLocale</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Streamer/StreamHeaders.php.html#189"><abbr title="Nzoom\Export\Streamer\StreamHeaders::flushHeaders">flushHeaders</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Streamer/StreamHeaders.php.html#168"><abbr title="Nzoom\Export\Streamer\StreamHeaders::sendPreparedHeaders">sendPreparedHeaders</abbr></a></td><td class="text-right">4</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#312"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::addNamedRanges">addNamedRanges</abbr></a></td><td class="text-right">4</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#200"><abbr title="Nzoom\Export\Streamer\FileStreamer::setStreamingOptimizationHeaders">setStreamingOptimizationHeaders</abbr></a></td><td class="text-right">2</td></tr>
       <tr><td><a href="Streamer/GeneratorFileStreamer.php.html#56"><abbr title="Nzoom\Export\Streamer\GeneratorFileStreamer::initializeGenerator">initializeGenerator</abbr></a></td><td class="text-right">2</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 9.2.32</a> using <a href="https://secure.php.net/" target="_top">PHP 7.4.33</a> and <a href="https://phpunit.de/">PHPUnit 9.6.23</a> at Fri Jun 20 8:40:28 UTC 2025.</small>
    </p>
   </footer>
  </div>
  <script src="_js/jquery.min.js?v=9.2.32" type="text/javascript"></script>
  <script src="_js/d3.min.js?v=9.2.32" type="text/javascript"></script>
  <script src="_js/nv.d3.min.js?v=9.2.32" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([2,0,0,2,0,0,0,0,3,5,6,2], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([89,0,1,0,1,1,1,5,9,12,22,208], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[83.69565217391305,38,"<a href=\"Adapter\/AbstractExportFormatAdapter.php.html#12\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter<\/a>"],[95.97701149425288,79,"<a href=\"Adapter\/CsvExportFormatAdapter.php.html#15\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter<\/a>"],[75.55110220440882,234,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#27\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter<\/a>"],[97.48743718592965,71,"<a href=\"Adapter\/JsonExportFormatAdapter.php.html#15\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter<\/a>"],[82.08955223880598,41,"<a href=\"DataFactory.php.html#17\">Nzoom\\Export\\DataFactory<\/a>"],[100,18,"<a href=\"Entity\/ExportColumn.php.html#10\">Nzoom\\Export\\Entity\\ExportColumn<\/a>"],[84.90566037735849,63,"<a href=\"Entity\/ExportData.php.html#12\">Nzoom\\Export\\Entity\\ExportData<\/a>"],[98.48484848484848,33,"<a href=\"Entity\/ExportHeader.php.html#11\">Nzoom\\Export\\Entity\\ExportHeader<\/a>"],[87.03703703703704,35,"<a href=\"Entity\/ExportRecord.php.html#11\">Nzoom\\Export\\Entity\\ExportRecord<\/a>"],[21.052631578947366,30,"<a href=\"Entity\/ExportTable.php.html#11\">Nzoom\\Export\\Entity\\ExportTable<\/a>"],[0,32,"<a href=\"Entity\/ExportTableCollection.php.html#11\">Nzoom\\Export\\Entity\\ExportTableCollection<\/a>"],[95.71428571428572,61,"<a href=\"Entity\/ExportValue.php.html#10\">Nzoom\\Export\\Entity\\ExportValue<\/a>"],[100,26,"<a href=\"ExportActionFactory.php.html#11\">Nzoom\\Export\\ExportActionFactory<\/a>"],[93.24324324324324,29,"<a href=\"ExportService.php.html#13\">Nzoom\\Export\\ExportService<\/a>"],[98.18181818181819,25,"<a href=\"Factory\/ExportFormatFactory.php.html#13\">Nzoom\\Export\\Factory\\ExportFormatFactory<\/a>"],[0,59,"<a href=\"Provider\/ModelTableProvider.php.html#18\">Nzoom\\Export\\Provider\\ModelTableProvider<\/a>"],[78.94736842105263,23,"<a href=\"Streamer\/FileStreamer.php.html#12\">Nzoom\\Export\\Streamer\\FileStreamer<\/a>"],[20.588235294117645,20,"<a href=\"Streamer\/GeneratorFileStreamer.php.html#11\">Nzoom\\Export\\Streamer\\GeneratorFileStreamer<\/a>"],[72.72727272727273,18,"<a href=\"Streamer\/PointerFileStreamer.php.html#11\">Nzoom\\Export\\Streamer\\PointerFileStreamer<\/a>"],[89.13043478260869,27,"<a href=\"Streamer\/StreamHeaders.php.html#11\">Nzoom\\Export\\Streamer\\StreamHeaders<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[100,2,"<a href=\"Adapter\/AbstractExportFormatAdapter.php.html#43\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::__construct<\/a>"],[100,1,"<a href=\"Adapter\/AbstractExportFormatAdapter.php.html#57\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::setConfiguration<\/a>"],[100,5,"<a href=\"Adapter\/AbstractExportFormatAdapter.php.html#70\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::getExportFilename<\/a>"],[0,3,"<a href=\"Adapter\/AbstractExportFormatAdapter.php.html#100\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::sendHeaders<\/a>"],[85.71428571428571,4,"<a href=\"Adapter\/AbstractExportFormatAdapter.php.html#127\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::handleExportError<\/a>"],[100,5,"<a href=\"Adapter\/AbstractExportFormatAdapter.php.html#161\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::getRecordHeaders<\/a>"],[100,1,"<a href=\"Adapter\/AbstractExportFormatAdapter.php.html#181\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::getFormatOptions<\/a>"],[100,3,"<a href=\"Adapter\/AbstractExportFormatAdapter.php.html#193\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::validateAndPrepareSaveTarget<\/a>"],[100,2,"<a href=\"Adapter\/AbstractExportFormatAdapter.php.html#213\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::validateStringTarget<\/a>"],[94.11764705882352,5,"<a href=\"Adapter\/AbstractExportFormatAdapter.php.html#231\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::validatePhpStreamWrapper<\/a>"],[83.33333333333334,3,"<a href=\"Adapter\/AbstractExportFormatAdapter.php.html#273\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::validateFilePath<\/a>"],[90,4,"<a href=\"Adapter\/AbstractExportFormatAdapter.php.html#295\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::validateFilePointer<\/a>"],[100,4,"<a href=\"Adapter\/CsvExportFormatAdapter.php.html#50\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::export<\/a>"],[90.47619047619048,19,"<a href=\"Adapter\/CsvExportFormatAdapter.php.html#85\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::extractCsvOptions<\/a>"],[93.33333333333333,6,"<a href=\"Adapter\/CsvExportFormatAdapter.php.html#128\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::writeCsvContent<\/a>"],[100,7,"<a href=\"Adapter\/CsvExportFormatAdapter.php.html#173\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::processExportDataRecords<\/a>"],[100,2,"<a href=\"Adapter\/CsvExportFormatAdapter.php.html#209\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::processExportRecord<\/a>"],[94.44444444444444,12,"<a href=\"Adapter\/CsvExportFormatAdapter.php.html#227\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::formatValueForCsv<\/a>"],[87.5,7,"<a href=\"Adapter\/CsvExportFormatAdapter.php.html#271\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::formatDateValue<\/a>"],[100,2,"<a href=\"Adapter\/CsvExportFormatAdapter.php.html#296\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::getDecimalPlaces<\/a>"],[75,4,"<a href=\"Adapter\/CsvExportFormatAdapter.php.html#312\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::getDelimiter<\/a>"],[100,6,"<a href=\"Adapter\/CsvExportFormatAdapter.php.html#339\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::normalizeDelimiter<\/a>"],[100,1,"<a href=\"Adapter\/CsvExportFormatAdapter.php.html#360\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::setDateFormat<\/a>"],[100,1,"<a href=\"Adapter\/CsvExportFormatAdapter.php.html#371\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::setDatetimeFormat<\/a>"],[100,1,"<a href=\"Adapter\/CsvExportFormatAdapter.php.html#381\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::getDateFormat<\/a>"],[100,1,"<a href=\"Adapter\/CsvExportFormatAdapter.php.html#391\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::getDatetimeFormat<\/a>"],[100,1,"<a href=\"Adapter\/CsvExportFormatAdapter.php.html#399\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::getSupportedExtensions<\/a>"],[100,1,"<a href=\"Adapter\/CsvExportFormatAdapter.php.html#407\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::getMimeType<\/a>"],[100,1,"<a href=\"Adapter\/CsvExportFormatAdapter.php.html#416\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::getDefaultExtension<\/a>"],[100,1,"<a href=\"Adapter\/CsvExportFormatAdapter.php.html#424\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::supportsFormat<\/a>"],[100,1,"<a href=\"Adapter\/CsvExportFormatAdapter.php.html#432\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::getFormatName<\/a>"],[100,1,"<a href=\"Adapter\/CsvExportFormatAdapter.php.html#440\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::getFormatOptions<\/a>"],[93.75,5,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#56\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::export<\/a>"],[63.63636363636363,11,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#106\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::extractSizingOptions<\/a>"],[90,2,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#138\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::createSpreadsheet<\/a>"],[50,4,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#172\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::setSpreadsheetLocale<\/a>"],[11.11111111111111,4,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#195\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::getApplicationLocale<\/a>"],[87.5,2,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#246\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::setDocumentProperties<\/a>"],[100,1,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#267\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::processExportData<\/a>"],[100,2,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#297\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::addHeaders<\/a>"],[68.75,4,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#312\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::addNamedRanges<\/a>"],[100,1,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#348\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::styleHeaderRow<\/a>"],[70.58823529411765,8,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#364\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::processExportDataRecords<\/a>"],[71.42857142857143,3,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#413\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::processExportRecord<\/a>"],[75,17,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#438\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::setCellValueWithFormatting<\/a>"],[60,5,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#499\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::setCellDateValue<\/a>"],[89.47368421052632,15,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#527\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::getExcelFormatFromExportValue<\/a>"],[100,3,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#578\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::convertCustomNumberFormat<\/a>"],[100,1,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#601\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::convertCustomDateFormat<\/a>"],[100,2,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#632\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::handleExportRecordCellError<\/a>"],[100,2,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#650\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::finalizeExportDataColumns<\/a>"],[38.46153846153847,5,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#674\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::applyColumnWidthConstraints<\/a>"],[76,8,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#711\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::applyRowHeightConstraints<\/a>"],[100,3,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#768\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::applyVerticalAlignment<\/a>"],[0,4,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#787\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::processExportTables<\/a>"],[0,5,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#811\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::collectTablesByType<\/a>"],[0,4,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#841\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::createTableWorksheet<\/a>"],[0,1,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#869\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::sanitizeWorksheetName<\/a>"],[0,3,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#883\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::populateTableWorksheet<\/a>"],[0,2,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#917\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::addTableDataToWorksheet<\/a>"],[100,4,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#937\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::createWriter<\/a>"],[90,4,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#958\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::handleSpreadsheetError<\/a>"],[100,83,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#988\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::getExcelFormatting<\/a>"],[100,3,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#1090\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::optimizeMemoryForExport<\/a>"],[90,4,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#1113\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::convertToBytes<\/a>"],[100,1,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#1136\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::getSupportedExtensions<\/a>"],[100,4,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#1144\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::getMimeType<\/a>"],[100,1,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#1161\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::getDefaultExtension<\/a>"],[100,1,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#1169\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::supportsFormat<\/a>"],[100,1,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#1177\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::getFormatName<\/a>"],[100,1,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#1185\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::getFormatOptions<\/a>"],[100,4,"<a href=\"Adapter\/JsonExportFormatAdapter.php.html#30\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::export<\/a>"],[100,7,"<a href=\"Adapter\/JsonExportFormatAdapter.php.html#65\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::extractJsonOptions<\/a>"],[93.33333333333333,6,"<a href=\"Adapter\/JsonExportFormatAdapter.php.html#89\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::writeJsonContent<\/a>"],[100,5,"<a href=\"Adapter\/JsonExportFormatAdapter.php.html#129\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::prepareJsonData<\/a>"],[90.9090909090909,5,"<a href=\"Adapter\/JsonExportFormatAdapter.php.html#148\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::prepareArrayStructure<\/a>"],[100,2,"<a href=\"Adapter\/JsonExportFormatAdapter.php.html#176\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::prepareObjectStructure<\/a>"],[100,6,"<a href=\"Adapter\/JsonExportFormatAdapter.php.html#201\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::prepareNestedStructure<\/a>"],[88.88888888888889,3,"<a href=\"Adapter\/JsonExportFormatAdapter.php.html#233\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::processExportRecord<\/a>"],[100,9,"<a href=\"Adapter\/JsonExportFormatAdapter.php.html#259\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::formatValueForJson<\/a>"],[90.9090909090909,7,"<a href=\"Adapter\/JsonExportFormatAdapter.php.html#297\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::formatDateValue<\/a>"],[100,1,"<a href=\"Adapter\/JsonExportFormatAdapter.php.html#324\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::addMetadata<\/a>"],[100,3,"<a href=\"Adapter\/JsonExportFormatAdapter.php.html#338\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::getMetadata<\/a>"],[96.29629629629629,7,"<a href=\"Adapter\/JsonExportFormatAdapter.php.html#373\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::getJsonOptions<\/a>"],[100,1,"<a href=\"Adapter\/JsonExportFormatAdapter.php.html#428\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::getSupportedExtensions<\/a>"],[100,1,"<a href=\"Adapter\/JsonExportFormatAdapter.php.html#436\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::getMimeType<\/a>"],[100,1,"<a href=\"Adapter\/JsonExportFormatAdapter.php.html#445\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::getDefaultExtension<\/a>"],[100,1,"<a href=\"Adapter\/JsonExportFormatAdapter.php.html#453\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::supportsFormat<\/a>"],[100,1,"<a href=\"Adapter\/JsonExportFormatAdapter.php.html#461\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::getFormatName<\/a>"],[100,1,"<a href=\"Adapter\/JsonExportFormatAdapter.php.html#469\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::getFormatOptions<\/a>"],[100,1,"<a href=\"DataFactory.php.html#44\">Nzoom\\Export\\DataFactory::__construct<\/a>"],[0,1,"<a href=\"DataFactory.php.html#55\">Nzoom\\Export\\DataFactory::setTableProvider<\/a>"],[0,1,"<a href=\"DataFactory.php.html#66\">Nzoom\\Export\\DataFactory::enableTables<\/a>"],[0,1,"<a href=\"DataFactory.php.html#74\">Nzoom\\Export\\DataFactory::disableTables<\/a>"],[100,2,"<a href=\"DataFactory.php.html#85\">Nzoom\\Export\\DataFactory::isTablesEnabled<\/a>"],[0,1,"<a href=\"DataFactory.php.html#96\">Nzoom\\Export\\DataFactory::withModelTableProvider<\/a>"],[0,2,"<a href=\"DataFactory.php.html#111\">Nzoom\\Export\\DataFactory::createWithTables<\/a>"],[90.9090909090909,3,"<a href=\"DataFactory.php.html#127\">Nzoom\\Export\\DataFactory::__invoke<\/a>"],[100,6,"<a href=\"DataFactory.php.html#158\">Nzoom\\Export\\DataFactory::createHeaderFromOutlook<\/a>"],[100,2,"<a href=\"DataFactory.php.html#193\">Nzoom\\Export\\DataFactory::processModelsChunk<\/a>"],[93.75,6,"<a href=\"DataFactory.php.html#207\">Nzoom\\Export\\DataFactory::createRecordFromModel<\/a>"],[0,5,"<a href=\"DataFactory.php.html#248\">Nzoom\\Export\\DataFactory::extractTablesForRecord<\/a>"],[100,1,"<a href=\"DataFactory.php.html#272\">Nzoom\\Export\\DataFactory::mapFieldTypeToExportType<\/a>"],[100,1,"<a href=\"DataFactory.php.html#301\">Nzoom\\Export\\DataFactory::setChunkSize<\/a>"],[95,3,"<a href=\"DataFactory.php.html#315\">Nzoom\\Export\\DataFactory::createStreaming<\/a>"],[92.85714285714286,5,"<a href=\"DataFactory.php.html#367\">Nzoom\\Export\\DataFactory::createCursorStreaming<\/a>"],[100,1,"<a href=\"Entity\/ExportColumn.php.html#53\">Nzoom\\Export\\Entity\\ExportColumn::__construct<\/a>"],[100,1,"<a href=\"Entity\/ExportColumn.php.html#74\">Nzoom\\Export\\Entity\\ExportColumn::getVarName<\/a>"],[100,2,"<a href=\"Entity\/ExportColumn.php.html#86\">Nzoom\\Export\\Entity\\ExportColumn::setVarName<\/a>"],[100,1,"<a href=\"Entity\/ExportColumn.php.html#100\">Nzoom\\Export\\Entity\\ExportColumn::getLabel<\/a>"],[100,1,"<a href=\"Entity\/ExportColumn.php.html#110\">Nzoom\\Export\\Entity\\ExportColumn::setLabel<\/a>"],[100,1,"<a href=\"Entity\/ExportColumn.php.html#120\">Nzoom\\Export\\Entity\\ExportColumn::getType<\/a>"],[100,2,"<a href=\"Entity\/ExportColumn.php.html#131\">Nzoom\\Export\\Entity\\ExportColumn::setType<\/a>"],[100,1,"<a href=\"Entity\/ExportColumn.php.html#150\">Nzoom\\Export\\Entity\\ExportColumn::getFormat<\/a>"],[100,1,"<a href=\"Entity\/ExportColumn.php.html#161\">Nzoom\\Export\\Entity\\ExportColumn::setFormat<\/a>"],[100,1,"<a href=\"Entity\/ExportColumn.php.html#171\">Nzoom\\Export\\Entity\\ExportColumn::getWidth<\/a>"],[100,1,"<a href=\"Entity\/ExportColumn.php.html#181\">Nzoom\\Export\\Entity\\ExportColumn::setWidth<\/a>"],[100,1,"<a href=\"Entity\/ExportColumn.php.html#191\">Nzoom\\Export\\Entity\\ExportColumn::getStyles<\/a>"],[100,1,"<a href=\"Entity\/ExportColumn.php.html#201\">Nzoom\\Export\\Entity\\ExportColumn::setStyles<\/a>"],[100,1,"<a href=\"Entity\/ExportColumn.php.html#212\">Nzoom\\Export\\Entity\\ExportColumn::addStyle<\/a>"],[100,1,"<a href=\"Entity\/ExportColumn.php.html#223\">Nzoom\\Export\\Entity\\ExportColumn::validateValue<\/a>"],[100,1,"<a href=\"Entity\/ExportColumn.php.html#235\">Nzoom\\Export\\Entity\\ExportColumn::createValue<\/a>"],[100,1,"<a href=\"Entity\/ExportData.php.html#60\">Nzoom\\Export\\Entity\\ExportData::__construct<\/a>"],[100,1,"<a href=\"Entity\/ExportData.php.html#74\">Nzoom\\Export\\Entity\\ExportData::setRecordProvider<\/a>"],[100,1,"<a href=\"Entity\/ExportData.php.html#91\">Nzoom\\Export\\Entity\\ExportData::isLazy<\/a>"],[100,1,"<a href=\"Entity\/ExportData.php.html#101\">Nzoom\\Export\\Entity\\ExportData::getPageSize<\/a>"],[100,1,"<a href=\"Entity\/ExportData.php.html#112\">Nzoom\\Export\\Entity\\ExportData::setPageSize<\/a>"],[100,1,"<a href=\"Entity\/ExportData.php.html#123\">Nzoom\\Export\\Entity\\ExportData::getHeader<\/a>"],[100,1,"<a href=\"Entity\/ExportData.php.html#133\">Nzoom\\Export\\Entity\\ExportData::setHeader<\/a>"],[100,1,"<a href=\"Entity\/ExportData.php.html#143\">Nzoom\\Export\\Entity\\ExportData::getMetadata<\/a>"],[100,1,"<a href=\"Entity\/ExportData.php.html#153\">Nzoom\\Export\\Entity\\ExportData::setMetadata<\/a>"],[100,1,"<a href=\"Entity\/ExportData.php.html#165\">Nzoom\\Export\\Entity\\ExportData::getMetadataValue<\/a>"],[100,1,"<a href=\"Entity\/ExportData.php.html#176\">Nzoom\\Export\\Entity\\ExportData::setMetadataValue<\/a>"],[80,4,"<a href=\"Entity\/ExportData.php.html#190\">Nzoom\\Export\\Entity\\ExportData::addRecord<\/a>"],[100,3,"<a href=\"Entity\/ExportData.php.html#209\">Nzoom\\Export\\Entity\\ExportData::getRecords<\/a>"],[100,4,"<a href=\"Entity\/ExportData.php.html#230\">Nzoom\\Export\\Entity\\ExportData::getRecordAt<\/a>"],[100,2,"<a href=\"Entity\/ExportData.php.html#252\">Nzoom\\Export\\Entity\\ExportData::count<\/a>"],[100,2,"<a href=\"Entity\/ExportData.php.html#269\">Nzoom\\Export\\Entity\\ExportData::createRecord<\/a>"],[100,2,"<a href=\"Entity\/ExportData.php.html#285\">Nzoom\\Export\\Entity\\ExportData::isEmpty<\/a>"],[80.76923076923077,15,"<a href=\"Entity\/ExportData.php.html#301\">Nzoom\\Export\\Entity\\ExportData::sortByColumn<\/a>"],[100,1,"<a href=\"Entity\/ExportData.php.html#349\">Nzoom\\Export\\Entity\\ExportData::filter<\/a>"],[75,3,"<a href=\"Entity\/ExportData.php.html#360\">Nzoom\\Export\\Entity\\ExportData::validate<\/a>"],[100,3,"<a href=\"Entity\/ExportData.php.html#378\">Nzoom\\Export\\Entity\\ExportData::toArray<\/a>"],[100,2,"<a href=\"Entity\/ExportData.php.html#398\">Nzoom\\Export\\Entity\\ExportData::getIterator<\/a>"],[0,1,"<a href=\"Entity\/ExportData.php.html#412\">Nzoom\\Export\\Entity\\ExportData::enableTables<\/a>"],[0,1,"<a href=\"Entity\/ExportData.php.html#421\">Nzoom\\Export\\Entity\\ExportData::disableTables<\/a>"],[100,1,"<a href=\"Entity\/ExportData.php.html#432\">Nzoom\\Export\\Entity\\ExportData::isTablesEnabled<\/a>"],[0,1,"<a href=\"Entity\/ExportData.php.html#442\">Nzoom\\Export\\Entity\\ExportData::getTableConfig<\/a>"],[0,1,"<a href=\"Entity\/ExportData.php.html#452\">Nzoom\\Export\\Entity\\ExportData::setTableConfig<\/a>"],[0,1,"<a href=\"Entity\/ExportData.php.html#464\">Nzoom\\Export\\Entity\\ExportData::getTableConfigValue<\/a>"],[0,1,"<a href=\"Entity\/ExportData.php.html#475\">Nzoom\\Export\\Entity\\ExportData::setTableConfigValue<\/a>"],[90,4,"<a href=\"Entity\/ExportData.php.html#485\">Nzoom\\Export\\Entity\\ExportData::getLazyIterator<\/a>"],[100,1,"<a href=\"Entity\/ExportHeader.php.html#44\">Nzoom\\Export\\Entity\\ExportHeader::__construct<\/a>"],[100,2,"<a href=\"Entity\/ExportHeader.php.html#57\">Nzoom\\Export\\Entity\\ExportHeader::addColumn<\/a>"],[100,1,"<a href=\"Entity\/ExportHeader.php.html#79\">Nzoom\\Export\\Entity\\ExportHeader::hasColumn<\/a>"],[100,1,"<a href=\"Entity\/ExportHeader.php.html#89\">Nzoom\\Export\\Entity\\ExportHeader::getColumns<\/a>"],[100,1,"<a href=\"Entity\/ExportHeader.php.html#100\">Nzoom\\Export\\Entity\\ExportHeader::getColumnAt<\/a>"],[100,2,"<a href=\"Entity\/ExportHeader.php.html#111\">Nzoom\\Export\\Entity\\ExportHeader::getColumnByVarName<\/a>"],[100,1,"<a href=\"Entity\/ExportHeader.php.html#125\">Nzoom\\Export\\Entity\\ExportHeader::getBackgroundColor<\/a>"],[100,1,"<a href=\"Entity\/ExportHeader.php.html#135\">Nzoom\\Export\\Entity\\ExportHeader::setBackgroundColor<\/a>"],[100,1,"<a href=\"Entity\/ExportHeader.php.html#145\">Nzoom\\Export\\Entity\\ExportHeader::getStyles<\/a>"],[100,1,"<a href=\"Entity\/ExportHeader.php.html#155\">Nzoom\\Export\\Entity\\ExportHeader::setStyles<\/a>"],[100,1,"<a href=\"Entity\/ExportHeader.php.html#166\">Nzoom\\Export\\Entity\\ExportHeader::addStyle<\/a>"],[100,1,"<a href=\"Entity\/ExportHeader.php.html#176\">Nzoom\\Export\\Entity\\ExportHeader::count<\/a>"],[100,1,"<a href=\"Entity\/ExportHeader.php.html#186\">Nzoom\\Export\\Entity\\ExportHeader::getLabels<\/a>"],[100,1,"<a href=\"Entity\/ExportHeader.php.html#198\">Nzoom\\Export\\Entity\\ExportHeader::getVarNames<\/a>"],[100,1,"<a href=\"Entity\/ExportHeader.php.html#210\">Nzoom\\Export\\Entity\\ExportHeader::getTypes<\/a>"],[100,6,"<a href=\"Entity\/ExportHeader.php.html#223\">Nzoom\\Export\\Entity\\ExportHeader::reorderColumns<\/a>"],[88.88888888888889,5,"<a href=\"Entity\/ExportHeader.php.html#264\">Nzoom\\Export\\Entity\\ExportHeader::validateRecord<\/a>"],[100,1,"<a href=\"Entity\/ExportHeader.php.html#291\">Nzoom\\Export\\Entity\\ExportHeader::rewind<\/a>"],[100,1,"<a href=\"Entity\/ExportHeader.php.html#301\">Nzoom\\Export\\Entity\\ExportHeader::current<\/a>"],[100,1,"<a href=\"Entity\/ExportHeader.php.html#311\">Nzoom\\Export\\Entity\\ExportHeader::key<\/a>"],[100,1,"<a href=\"Entity\/ExportHeader.php.html#319\">Nzoom\\Export\\Entity\\ExportHeader::next<\/a>"],[100,1,"<a href=\"Entity\/ExportHeader.php.html#329\">Nzoom\\Export\\Entity\\ExportHeader::valid<\/a>"],[100,1,"<a href=\"Entity\/ExportRecord.php.html#43\">Nzoom\\Export\\Entity\\ExportRecord::__construct<\/a>"],[100,2,"<a href=\"Entity\/ExportRecord.php.html#57\">Nzoom\\Export\\Entity\\ExportRecord::addValue<\/a>"],[100,4,"<a href=\"Entity\/ExportRecord.php.html#76\">Nzoom\\Export\\Entity\\ExportRecord::setValueAt<\/a>"],[100,2,"<a href=\"Entity\/ExportRecord.php.html#103\">Nzoom\\Export\\Entity\\ExportRecord::setValueByColumnName<\/a>"],[100,1,"<a href=\"Entity\/ExportRecord.php.html#121\">Nzoom\\Export\\Entity\\ExportRecord::getValues<\/a>"],[100,1,"<a href=\"Entity\/ExportRecord.php.html#132\">Nzoom\\Export\\Entity\\ExportRecord::getValueAt<\/a>"],[100,2,"<a href=\"Entity\/ExportRecord.php.html#143\">Nzoom\\Export\\Entity\\ExportRecord::getValueByColumnName<\/a>"],[100,1,"<a href=\"Entity\/ExportRecord.php.html#158\">Nzoom\\Export\\Entity\\ExportRecord::hasValue<\/a>"],[100,1,"<a href=\"Entity\/ExportRecord.php.html#168\">Nzoom\\Export\\Entity\\ExportRecord::getRawValues<\/a>"],[100,1,"<a href=\"Entity\/ExportRecord.php.html#180\">Nzoom\\Export\\Entity\\ExportRecord::getFormattedValues<\/a>"],[100,1,"<a href=\"Entity\/ExportRecord.php.html#192\">Nzoom\\Export\\Entity\\ExportRecord::getMetadata<\/a>"],[100,1,"<a href=\"Entity\/ExportRecord.php.html#202\">Nzoom\\Export\\Entity\\ExportRecord::setMetadata<\/a>"],[100,1,"<a href=\"Entity\/ExportRecord.php.html#214\">Nzoom\\Export\\Entity\\ExportRecord::getMetadataValue<\/a>"],[100,1,"<a href=\"Entity\/ExportRecord.php.html#225\">Nzoom\\Export\\Entity\\ExportRecord::setMetadataValue<\/a>"],[0,1,"<a href=\"Entity\/ExportRecord.php.html#235\">Nzoom\\Export\\Entity\\ExportRecord::getTableCollection<\/a>"],[0,1,"<a href=\"Entity\/ExportRecord.php.html#245\">Nzoom\\Export\\Entity\\ExportRecord::setTableCollection<\/a>"],[0,2,"<a href=\"Entity\/ExportRecord.php.html#255\">Nzoom\\Export\\Entity\\ExportRecord::hasTables<\/a>"],[0,2,"<a href=\"Entity\/ExportRecord.php.html#266\">Nzoom\\Export\\Entity\\ExportRecord::getTable<\/a>"],[0,2,"<a href=\"Entity\/ExportRecord.php.html#276\">Nzoom\\Export\\Entity\\ExportRecord::addTable<\/a>"],[100,1,"<a href=\"Entity\/ExportRecord.php.html#291\">Nzoom\\Export\\Entity\\ExportRecord::validate<\/a>"],[100,1,"<a href=\"Entity\/ExportRecord.php.html#301\">Nzoom\\Export\\Entity\\ExportRecord::count<\/a>"],[100,1,"<a href=\"Entity\/ExportRecord.php.html#311\">Nzoom\\Export\\Entity\\ExportRecord::rewind<\/a>"],[100,1,"<a href=\"Entity\/ExportRecord.php.html#321\">Nzoom\\Export\\Entity\\ExportRecord::current<\/a>"],[100,1,"<a href=\"Entity\/ExportRecord.php.html#331\">Nzoom\\Export\\Entity\\ExportRecord::key<\/a>"],[100,1,"<a href=\"Entity\/ExportRecord.php.html#339\">Nzoom\\Export\\Entity\\ExportRecord::next<\/a>"],[100,1,"<a href=\"Entity\/ExportRecord.php.html#349\">Nzoom\\Export\\Entity\\ExportRecord::valid<\/a>"],[100,1,"<a href=\"Entity\/ExportTable.php.html#52\">Nzoom\\Export\\Entity\\ExportTable::__construct<\/a>"],[0,1,"<a href=\"Entity\/ExportTable.php.html#71\">Nzoom\\Export\\Entity\\ExportTable::getTableType<\/a>"],[0,1,"<a href=\"Entity\/ExportTable.php.html#81\">Nzoom\\Export\\Entity\\ExportTable::getTableName<\/a>"],[0,1,"<a href=\"Entity\/ExportTable.php.html#91\">Nzoom\\Export\\Entity\\ExportTable::setTableName<\/a>"],[0,1,"<a href=\"Entity\/ExportTable.php.html#101\">Nzoom\\Export\\Entity\\ExportTable::getHeader<\/a>"],[0,1,"<a href=\"Entity\/ExportTable.php.html#111\">Nzoom\\Export\\Entity\\ExportTable::setHeader<\/a>"],[0,1,"<a href=\"Entity\/ExportTable.php.html#121\">Nzoom\\Export\\Entity\\ExportTable::getParentReference<\/a>"],[0,1,"<a href=\"Entity\/ExportTable.php.html#131\">Nzoom\\Export\\Entity\\ExportTable::setParentReference<\/a>"],[0,3,"<a href=\"Entity\/ExportTable.php.html#143\">Nzoom\\Export\\Entity\\ExportTable::addRecord<\/a>"],[0,1,"<a href=\"Entity\/ExportTable.php.html#157\">Nzoom\\Export\\Entity\\ExportTable::getRecords<\/a>"],[0,4,"<a href=\"Entity\/ExportTable.php.html#169\">Nzoom\\Export\\Entity\\ExportTable::setRecords<\/a>"],[0,1,"<a href=\"Entity\/ExportTable.php.html#185\">Nzoom\\Export\\Entity\\ExportTable::clearRecords<\/a>"],[0,1,"<a href=\"Entity\/ExportTable.php.html#195\">Nzoom\\Export\\Entity\\ExportTable::hasRecords<\/a>"],[100,1,"<a href=\"Entity\/ExportTable.php.html#205\">Nzoom\\Export\\Entity\\ExportTable::getMetadata<\/a>"],[0,1,"<a href=\"Entity\/ExportTable.php.html#215\">Nzoom\\Export\\Entity\\ExportTable::setMetadata<\/a>"],[100,1,"<a href=\"Entity\/ExportTable.php.html#227\">Nzoom\\Export\\Entity\\ExportTable::getMetadataValue<\/a>"],[100,1,"<a href=\"Entity\/ExportTable.php.html#238\">Nzoom\\Export\\Entity\\ExportTable::setMetadataValue<\/a>"],[0,3,"<a href=\"Entity\/ExportTable.php.html#248\">Nzoom\\Export\\Entity\\ExportTable::validate<\/a>"],[0,3,"<a href=\"Entity\/ExportTable.php.html#266\">Nzoom\\Export\\Entity\\ExportTable::toArray<\/a>"],[0,1,"<a href=\"Entity\/ExportTable.php.html#286\">Nzoom\\Export\\Entity\\ExportTable::count<\/a>"],[0,1,"<a href=\"Entity\/ExportTable.php.html#296\">Nzoom\\Export\\Entity\\ExportTable::getIterator<\/a>"],[0,1,"<a href=\"Entity\/ExportTableCollection.php.html#28\">Nzoom\\Export\\Entity\\ExportTableCollection::__construct<\/a>"],[0,2,"<a href=\"Entity\/ExportTableCollection.php.html#39\">Nzoom\\Export\\Entity\\ExportTableCollection::addTable<\/a>"],[0,1,"<a href=\"Entity\/ExportTableCollection.php.html#59\">Nzoom\\Export\\Entity\\ExportTableCollection::getTable<\/a>"],[0,1,"<a href=\"Entity\/ExportTableCollection.php.html#70\">Nzoom\\Export\\Entity\\ExportTableCollection::hasTable<\/a>"],[0,2,"<a href=\"Entity\/ExportTableCollection.php.html#81\">Nzoom\\Export\\Entity\\ExportTableCollection::removeTable<\/a>"],[0,1,"<a href=\"Entity\/ExportTableCollection.php.html#96\">Nzoom\\Export\\Entity\\ExportTableCollection::getTables<\/a>"],[0,1,"<a href=\"Entity\/ExportTableCollection.php.html#106\">Nzoom\\Export\\Entity\\ExportTableCollection::getTableTypes<\/a>"],[0,1,"<a href=\"Entity\/ExportTableCollection.php.html#114\">Nzoom\\Export\\Entity\\ExportTableCollection::clearTables<\/a>"],[0,1,"<a href=\"Entity\/ExportTableCollection.php.html#124\">Nzoom\\Export\\Entity\\ExportTableCollection::hasTables<\/a>"],[0,1,"<a href=\"Entity\/ExportTableCollection.php.html#134\">Nzoom\\Export\\Entity\\ExportTableCollection::getTablesWithRecords<\/a>"],[0,1,"<a href=\"Entity\/ExportTableCollection.php.html#146\">Nzoom\\Export\\Entity\\ExportTableCollection::getTableTypesWithRecords<\/a>"],[0,1,"<a href=\"Entity\/ExportTableCollection.php.html#157\">Nzoom\\Export\\Entity\\ExportTableCollection::getMetadata<\/a>"],[0,1,"<a href=\"Entity\/ExportTableCollection.php.html#167\">Nzoom\\Export\\Entity\\ExportTableCollection::setMetadata<\/a>"],[0,1,"<a href=\"Entity\/ExportTableCollection.php.html#179\">Nzoom\\Export\\Entity\\ExportTableCollection::getMetadataValue<\/a>"],[0,1,"<a href=\"Entity\/ExportTableCollection.php.html#190\">Nzoom\\Export\\Entity\\ExportTableCollection::setMetadataValue<\/a>"],[0,3,"<a href=\"Entity\/ExportTableCollection.php.html#200\">Nzoom\\Export\\Entity\\ExportTableCollection::validate<\/a>"],[0,2,"<a href=\"Entity\/ExportTableCollection.php.html#216\">Nzoom\\Export\\Entity\\ExportTableCollection::getTotalRecordCount<\/a>"],[0,1,"<a href=\"Entity\/ExportTableCollection.php.html#230\">Nzoom\\Export\\Entity\\ExportTableCollection::count<\/a>"],[0,1,"<a href=\"Entity\/ExportTableCollection.php.html#240\">Nzoom\\Export\\Entity\\ExportTableCollection::getIterator<\/a>"],[0,2,"<a href=\"Entity\/ExportTableCollection.php.html#252\">Nzoom\\Export\\Entity\\ExportTableCollection::toArray<\/a>"],[0,6,"<a href=\"Entity\/ExportTableCollection.php.html#274\">Nzoom\\Export\\Entity\\ExportTableCollection::fromArray<\/a>"],[100,1,"<a href=\"Entity\/ExportValue.php.html#58\">Nzoom\\Export\\Entity\\ExportValue::getValidTypes<\/a>"],[100,1,"<a href=\"Entity\/ExportValue.php.html#70\">Nzoom\\Export\\Entity\\ExportValue::__construct<\/a>"],[100,1,"<a href=\"Entity\/ExportValue.php.html#82\">Nzoom\\Export\\Entity\\ExportValue::getValue<\/a>"],[100,1,"<a href=\"Entity\/ExportValue.php.html#92\">Nzoom\\Export\\Entity\\ExportValue::setValue<\/a>"],[100,1,"<a href=\"Entity\/ExportValue.php.html#102\">Nzoom\\Export\\Entity\\ExportValue::getType<\/a>"],[100,3,"<a href=\"Entity\/ExportValue.php.html#113\">Nzoom\\Export\\Entity\\ExportValue::setType<\/a>"],[100,1,"<a href=\"Entity\/ExportValue.php.html#131\">Nzoom\\Export\\Entity\\ExportValue::getFormat<\/a>"],[100,1,"<a href=\"Entity\/ExportValue.php.html#142\">Nzoom\\Export\\Entity\\ExportValue::setFormat<\/a>"],[100,1,"<a href=\"Entity\/ExportValue.php.html#153\">Nzoom\\Export\\Entity\\ExportValue::isNull<\/a>"],[95,27,"<a href=\"Entity\/ExportValue.php.html#163\">Nzoom\\Export\\Entity\\ExportValue::validate<\/a>"],[90,15,"<a href=\"Entity\/ExportValue.php.html#196\">Nzoom\\Export\\Entity\\ExportValue::getFormattedValue<\/a>"],[100,8,"<a href=\"Entity\/ExportValue.php.html#233\">Nzoom\\Export\\Entity\\ExportValue::__toString<\/a>"],[100,3,"<a href=\"ExportActionFactory.php.html#55\">Nzoom\\Export\\ExportActionFactory::__construct<\/a>"],[100,1,"<a href=\"ExportActionFactory.php.html#85\">Nzoom\\Export\\ExportActionFactory::setModelName<\/a>"],[100,1,"<a href=\"ExportActionFactory.php.html#97\">Nzoom\\Export\\ExportActionFactory::setModelFactoryName<\/a>"],[100,1,"<a href=\"ExportActionFactory.php.html#110\">Nzoom\\Export\\ExportActionFactory::__invoke<\/a>"],[100,1,"<a href=\"ExportActionFactory.php.html#122\">Nzoom\\Export\\ExportActionFactory::createExportAction<\/a>"],[100,1,"<a href=\"ExportActionFactory.php.html#141\">Nzoom\\Export\\ExportActionFactory::prepareExportOptions<\/a>"],[100,3,"<a href=\"ExportActionFactory.php.html#177\">Nzoom\\Export\\ExportActionFactory::initializeFilterVisibility<\/a>"],[100,2,"<a href=\"ExportActionFactory.php.html#194\">Nzoom\\Export\\ExportActionFactory::firstOrZero<\/a>"],[100,4,"<a href=\"ExportActionFactory.php.html#209\">Nzoom\\Export\\ExportActionFactory::getPluginOptions<\/a>"],[100,4,"<a href=\"ExportActionFactory.php.html#280\">Nzoom\\Export\\ExportActionFactory::buildBaseExportOptions<\/a>"],[100,2,"<a href=\"ExportActionFactory.php.html#329\">Nzoom\\Export\\ExportActionFactory::getFormatOptions<\/a>"],[100,1,"<a href=\"ExportActionFactory.php.html#364\">Nzoom\\Export\\ExportActionFactory::getGroupTablesOptions<\/a>"],[100,2,"<a href=\"ExportActionFactory.php.html#397\">Nzoom\\Export\\ExportActionFactory::getDelimiterOptions<\/a>"],[100,1,"<a href=\"ExportService.php.html#69\">Nzoom\\Export\\ExportService::__construct<\/a>"],[100,1,"<a href=\"ExportService.php.html#84\">Nzoom\\Export\\ExportService::setModelName<\/a>"],[100,1,"<a href=\"ExportService.php.html#96\">Nzoom\\Export\\ExportService::setModelFactoryName<\/a>"],[100,1,"<a href=\"ExportService.php.html#110\">Nzoom\\Export\\ExportService::createExportAction<\/a>"],[100,1,"<a href=\"ExportService.php.html#127\">Nzoom\\Export\\ExportService::createExportData<\/a>"],[100,1,"<a href=\"ExportService.php.html#142\">Nzoom\\Export\\ExportService::createGeneratorFileStreamer<\/a>"],[75,3,"<a href=\"ExportService.php.html#156\">Nzoom\\Export\\ExportService::export<\/a>"],[75,2,"<a href=\"ExportService.php.html#181\">Nzoom\\Export\\ExportService::createTempStream<\/a>"],[100,3,"<a href=\"ExportService.php.html#201\">Nzoom\\Export\\ExportService::streamToBrowser<\/a>"],[100,2,"<a href=\"ExportService.php.html#232\">Nzoom\\Export\\ExportService::getFormatFactory<\/a>"],[100,1,"<a href=\"ExportService.php.html#247\">Nzoom\\Export\\ExportService::setFormatFactory<\/a>"],[100,2,"<a href=\"ExportService.php.html#259\">Nzoom\\Export\\ExportService::getAdapter<\/a>"],[100,1,"<a href=\"ExportService.php.html#273\">Nzoom\\Export\\ExportService::getSupportedFormats<\/a>"],[100,1,"<a href=\"ExportService.php.html#284\">Nzoom\\Export\\ExportService::isFormatSupported<\/a>"],[100,4,"<a href=\"ExportService.php.html#296\">Nzoom\\Export\\ExportService::getExportFilename<\/a>"],[84.61538461538461,4,"<a href=\"ExportService.php.html#326\">Nzoom\\Export\\ExportService::handleExportError<\/a>"],[100,1,"<a href=\"Factory\/ExportFormatFactory.php.html#44\">Nzoom\\Export\\Factory\\ExportFormatFactory::__construct<\/a>"],[100,3,"<a href=\"Factory\/ExportFormatFactory.php.html#60\">Nzoom\\Export\\Factory\\ExportFormatFactory::createAdapter<\/a>"],[100,3,"<a href=\"Factory\/ExportFormatFactory.php.html#94\">Nzoom\\Export\\Factory\\ExportFormatFactory::getAdapterClass<\/a>"],[91.66666666666666,6,"<a href=\"Factory\/ExportFormatFactory.php.html#112\">Nzoom\\Export\\Factory\\ExportFormatFactory::discoverAdapters<\/a>"],[100,4,"<a href=\"Factory\/ExportFormatFactory.php.html#148\">Nzoom\\Export\\Factory\\ExportFormatFactory::getClassNameFromFile<\/a>"],[100,2,"<a href=\"Factory\/ExportFormatFactory.php.html#171\">Nzoom\\Export\\Factory\\ExportFormatFactory::isValidAdapter<\/a>"],[100,3,"<a href=\"Factory\/ExportFormatFactory.php.html#186\">Nzoom\\Export\\Factory\\ExportFormatFactory::getSupportedFormats<\/a>"],[100,1,"<a href=\"Factory\/ExportFormatFactory.php.html#213\">Nzoom\\Export\\Factory\\ExportFormatFactory::isFormatSupported<\/a>"],[100,2,"<a href=\"Factory\/ExportFormatFactory.php.html#227\">Nzoom\\Export\\Factory\\ExportFormatFactory::createAdapterFromFilename<\/a>"],[0,1,"<a href=\"Provider\/ModelTableProvider.php.html#36\">Nzoom\\Export\\Provider\\ModelTableProvider::__construct<\/a>"],[0,1,"<a href=\"Provider\/ModelTableProvider.php.html#47\">Nzoom\\Export\\Provider\\ModelTableProvider::getDefaultConfig<\/a>"],[0,8,"<a href=\"Provider\/ModelTableProvider.php.html#61\">Nzoom\\Export\\Provider\\ModelTableProvider::getTablesForRecord<\/a>"],[0,7,"<a href=\"Provider\/ModelTableProvider.php.html#97\">Nzoom\\Export\\Provider\\ModelTableProvider::extractTable<\/a>"],[0,4,"<a href=\"Provider\/ModelTableProvider.php.html#148\">Nzoom\\Export\\Provider\\ModelTableProvider::createTableHeader<\/a>"],[0,7,"<a href=\"Provider\/ModelTableProvider.php.html#182\">Nzoom\\Export\\Provider\\ModelTableProvider::populateTableFromData<\/a>"],[0,3,"<a href=\"Provider\/ModelTableProvider.php.html#216\">Nzoom\\Export\\Provider\\ModelTableProvider::createRecordFromItem<\/a>"],[0,5,"<a href=\"Provider\/ModelTableProvider.php.html#246\">Nzoom\\Export\\Provider\\ModelTableProvider::extractValueFromItem<\/a>"],[0,13,"<a href=\"Provider\/ModelTableProvider.php.html#268\">Nzoom\\Export\\Provider\\ModelTableProvider::formatValue<\/a>"],[0,1,"<a href=\"Provider\/ModelTableProvider.php.html#307\">Nzoom\\Export\\Provider\\ModelTableProvider::getSupportedTableTypes<\/a>"],[0,1,"<a href=\"Provider\/ModelTableProvider.php.html#315\">Nzoom\\Export\\Provider\\ModelTableProvider::supportsTableType<\/a>"],[0,1,"<a href=\"Provider\/ModelTableProvider.php.html#323\">Nzoom\\Export\\Provider\\ModelTableProvider::getTableConfiguration<\/a>"],[0,7,"<a href=\"Provider\/ModelTableProvider.php.html#331\">Nzoom\\Export\\Provider\\ModelTableProvider::validateRecord<\/a>"],[100,1,"<a href=\"Streamer\/FileStreamer.php.html#60\">Nzoom\\Export\\Streamer\\FileStreamer::__construct<\/a>"],[100,2,"<a href=\"Streamer\/FileStreamer.php.html#71\">Nzoom\\Export\\Streamer\\FileStreamer::getHeaders<\/a>"],[0,1,"<a href=\"Streamer\/FileStreamer.php.html#85\">Nzoom\\Export\\Streamer\\FileStreamer::setHeaders<\/a>"],[0,1,"<a href=\"Streamer\/FileStreamer.php.html#96\">Nzoom\\Export\\Streamer\\FileStreamer::setTimeIncrement<\/a>"],[0,1,"<a href=\"Streamer\/FileStreamer.php.html#107\">Nzoom\\Export\\Streamer\\FileStreamer::setCacheExpires<\/a>"],[100,1,"<a href=\"Streamer\/FileStreamer.php.html#118\">Nzoom\\Export\\Streamer\\FileStreamer::setETag<\/a>"],[100,1,"<a href=\"Streamer\/FileStreamer.php.html#129\">Nzoom\\Export\\Streamer\\FileStreamer::setLastModified<\/a>"],[85.71428571428571,2,"<a href=\"Streamer\/FileStreamer.php.html#142\">Nzoom\\Export\\Streamer\\FileStreamer::stream<\/a>"],[100,0,"<a href=\"Streamer\/FileStreamer.php.html#168\">Nzoom\\Export\\Streamer\\FileStreamer::performStreaming<\/a>"],[100,2,"<a href=\"Streamer\/FileStreamer.php.html#175\">Nzoom\\Export\\Streamer\\FileStreamer::prepareForStreaming<\/a>"],[60,2,"<a href=\"Streamer\/FileStreamer.php.html#200\">Nzoom\\Export\\Streamer\\FileStreamer::setStreamingOptimizationHeaders<\/a>"],[100,2,"<a href=\"Streamer\/FileStreamer.php.html#225\">Nzoom\\Export\\Streamer\\FileStreamer::outputChunk<\/a>"],[100,1,"<a href=\"Streamer\/FileStreamer.php.html#243\">Nzoom\\Export\\Streamer\\FileStreamer::isClientConnected<\/a>"],[100,2,"<a href=\"Streamer\/FileStreamer.php.html#253\">Nzoom\\Export\\Streamer\\FileStreamer::increaseExecutionTime<\/a>"],[100,2,"<a href=\"Streamer\/FileStreamer.php.html#265\">Nzoom\\Export\\Streamer\\FileStreamer::cleanup<\/a>"],[0,1,"<a href=\"Streamer\/FileStreamer.php.html#278\">Nzoom\\Export\\Streamer\\FileStreamer::getFilename<\/a>"],[0,1,"<a href=\"Streamer\/FileStreamer.php.html#288\">Nzoom\\Export\\Streamer\\FileStreamer::getMimeType<\/a>"],[83.33333333333334,2,"<a href=\"Streamer\/GeneratorFileStreamer.php.html#36\">Nzoom\\Export\\Streamer\\GeneratorFileStreamer::__construct<\/a>"],[66.66666666666666,2,"<a href=\"Streamer\/GeneratorFileStreamer.php.html#56\">Nzoom\\Export\\Streamer\\GeneratorFileStreamer::initializeGenerator<\/a>"],[0,8,"<a href=\"Streamer\/GeneratorFileStreamer.php.html#68\">Nzoom\\Export\\Streamer\\GeneratorFileStreamer::performStreaming<\/a>"],[0,4,"<a href=\"Streamer\/GeneratorFileStreamer.php.html#98\">Nzoom\\Export\\Streamer\\GeneratorFileStreamer::cleanup<\/a>"],[0,2,"<a href=\"Streamer\/GeneratorFileStreamer.php.html#123\">Nzoom\\Export\\Streamer\\GeneratorFileStreamer::setTotalSize<\/a>"],[0,1,"<a href=\"Streamer\/GeneratorFileStreamer.php.html#140\">Nzoom\\Export\\Streamer\\GeneratorFileStreamer::getTotalSize<\/a>"],[0,1,"<a href=\"Streamer\/GeneratorFileStreamer.php.html#150\">Nzoom\\Export\\Streamer\\GeneratorFileStreamer::reset<\/a>"],[93.33333333333333,6,"<a href=\"Streamer\/PointerFileStreamer.php.html#36\">Nzoom\\Export\\Streamer\\PointerFileStreamer::__construct<\/a>"],[75,6,"<a href=\"Streamer\/PointerFileStreamer.php.html#71\">Nzoom\\Export\\Streamer\\PointerFileStreamer::performStreaming<\/a>"],[100,2,"<a href=\"Streamer\/PointerFileStreamer.php.html#93\">Nzoom\\Export\\Streamer\\PointerFileStreamer::cleanup<\/a>"],[0,1,"<a href=\"Streamer\/PointerFileStreamer.php.html#110\">Nzoom\\Export\\Streamer\\PointerFileStreamer::getChunkSize<\/a>"],[0,2,"<a href=\"Streamer\/PointerFileStreamer.php.html#121\">Nzoom\\Export\\Streamer\\PointerFileStreamer::setChunkSize<\/a>"],[0,1,"<a href=\"Streamer\/PointerFileStreamer.php.html#136\">Nzoom\\Export\\Streamer\\PointerFileStreamer::getTotalSize<\/a>"],[100,1,"<a href=\"Streamer\/StreamHeaders.php.html#25\">Nzoom\\Export\\Streamer\\StreamHeaders::addHeader<\/a>"],[100,1,"<a href=\"Streamer\/StreamHeaders.php.html#36\">Nzoom\\Export\\Streamer\\StreamHeaders::setHeaders<\/a>"],[100,1,"<a href=\"Streamer\/StreamHeaders.php.html#47\">Nzoom\\Export\\Streamer\\StreamHeaders::getHeader<\/a>"],[100,1,"<a href=\"Streamer\/StreamHeaders.php.html#57\">Nzoom\\Export\\Streamer\\StreamHeaders::getAll<\/a>"],[100,1,"<a href=\"Streamer\/StreamHeaders.php.html#67\">Nzoom\\Export\\Streamer\\StreamHeaders::clear<\/a>"],[100,1,"<a href=\"Streamer\/StreamHeaders.php.html#78\">Nzoom\\Export\\Streamer\\StreamHeaders::hasHeader<\/a>"],[100,1,"<a href=\"Streamer\/StreamHeaders.php.html#89\">Nzoom\\Export\\Streamer\\StreamHeaders::removeHeader<\/a>"],[100,1,"<a href=\"Streamer\/StreamHeaders.php.html#101\">Nzoom\\Export\\Streamer\\StreamHeaders::setFileContentHeaders<\/a>"],[100,2,"<a href=\"Streamer\/StreamHeaders.php.html#117\">Nzoom\\Export\\Streamer\\StreamHeaders::sanitizeFilename<\/a>"],[100,4,"<a href=\"Streamer\/StreamHeaders.php.html#138\">Nzoom\\Export\\Streamer\\StreamHeaders::prepareCacheHeaders<\/a>"],[40,3,"<a href=\"Streamer\/StreamHeaders.php.html#168\">Nzoom\\Export\\Streamer\\StreamHeaders::sendPreparedHeaders<\/a>"],[0,2,"<a href=\"Streamer\/StreamHeaders.php.html#189\">Nzoom\\Export\\Streamer\\StreamHeaders::flushHeaders<\/a>"],[100,1,"<a href=\"Streamer\/StreamHeaders.php.html#204\">Nzoom\\Export\\Streamer\\StreamHeaders::send304NotModified<\/a>"],[100,7,"<a href=\"Streamer\/StreamHeaders.php.html#223\">Nzoom\\Export\\Streamer\\StreamHeaders::handleCacheValidation<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>
