<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /var/www/Nzoom-Hella/_libs/Nzoom/Export</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="_css/bootstrap.min.css?v=9.2.32" rel="stylesheet" type="text/css">
  <link href="_css/nv.d3.min.css?v=9.2.32" rel="stylesheet" type="text/css">
  <link href="_css/style.css?v=9.2.32" rel="stylesheet" type="text/css">
  <link href="_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="index.html">/var/www/Nzoom-Hella/_libs/Nzoom/Export</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Adapter/AbstractExportFormatAdapter.php.html#12">Nzoom\Export\Adapter\AbstractExportFormatAdapter</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportActionFactory.php.html#11">Nzoom\Export\ExportActionFactory</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/PointerFileStreamer.php.html#11">Nzoom\Export\Streamer\PointerFileStreamer</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/GeneratorFileStreamer.php.html#11">Nzoom\Export\Streamer\GeneratorFileStreamer</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#12">Nzoom\Export\Streamer\FileStreamer</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Provider/ModelTableProvider.php.html#18">Nzoom\Export\Provider\ModelTableProvider</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Factory/ExportFormatFactory.php.html#13">Nzoom\Export\Factory\ExportFormatFactory</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportService.php.html#13">Nzoom\Export\ExportService</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportTableCollection.php.html#11">Nzoom\Export\Entity\ExportTableCollection</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/CsvExportFormatAdapter.php.html#15">Nzoom\Export\Adapter\CsvExportFormatAdapter</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportData.php.html#12">Nzoom\Export\Entity\ExportData</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DataFactory.php.html#17">Nzoom\Export\DataFactory</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/JsonExportFormatAdapter.php.html#15">Nzoom\Export\Adapter\JsonExportFormatAdapter</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#27">Nzoom\Export\Adapter\ExcelExportFormatAdapter</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/StreamHeaders.php.html#11">Nzoom\Export\Streamer\StreamHeaders</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportValue.php.html#10">Nzoom\Export\Entity\ExportValue</a></td><td class="text-right">21%</td></tr>
       <tr><td><a href="Entity/ExportRecord.php.html#11">Nzoom\Export\Entity\ExportRecord</a></td><td class="text-right">22%</td></tr>
       <tr><td><a href="Entity/ExportHeader.php.html#11">Nzoom\Export\Entity\ExportHeader</a></td><td class="text-right">28%</td></tr>
       <tr><td><a href="Entity/ExportColumn.php.html#10">Nzoom\Export\Entity\ExportColumn</a></td><td class="text-right">60%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#27">Nzoom\Export\Adapter\ExcelExportFormatAdapter</a></td><td class="text-right">54522</td></tr>
       <tr><td><a href="Adapter/CsvExportFormatAdapter.php.html#15">Nzoom\Export\Adapter\CsvExportFormatAdapter</a></td><td class="text-right">6320</td></tr>
       <tr><td><a href="Adapter/JsonExportFormatAdapter.php.html#15">Nzoom\Export\Adapter\JsonExportFormatAdapter</a></td><td class="text-right">5112</td></tr>
       <tr><td><a href="Provider/ModelTableProvider.php.html#18">Nzoom\Export\Provider\ModelTableProvider</a></td><td class="text-right">3422</td></tr>
       <tr><td><a href="Entity/ExportData.php.html#12">Nzoom\Export\Entity\ExportData</a></td><td class="text-right">3192</td></tr>
       <tr><td><a href="Entity/ExportValue.php.html#10">Nzoom\Export\Entity\ExportValue</a></td><td class="text-right">1865</td></tr>
       <tr><td><a href="Adapter/AbstractExportFormatAdapter.php.html#12">Nzoom\Export\Adapter\AbstractExportFormatAdapter</a></td><td class="text-right">1482</td></tr>
       <tr><td><a href="DataFactory.php.html#17">Nzoom\Export\DataFactory</a></td><td class="text-right">1122</td></tr>
       <tr><td><a href="Entity/ExportTableCollection.php.html#11">Nzoom\Export\Entity\ExportTableCollection</a></td><td class="text-right">1056</td></tr>
       <tr><td><a href="ExportService.php.html#13">Nzoom\Export\ExportService</a></td><td class="text-right">870</td></tr>
       <tr><td><a href="Streamer/StreamHeaders.php.html#11">Nzoom\Export\Streamer\StreamHeaders</a></td><td class="text-right">756</td></tr>
       <tr><td><a href="ExportActionFactory.php.html#11">Nzoom\Export\ExportActionFactory</a></td><td class="text-right">702</td></tr>
       <tr><td><a href="Factory/ExportFormatFactory.php.html#13">Nzoom\Export\Factory\ExportFormatFactory</a></td><td class="text-right">650</td></tr>
       <tr><td><a href="Entity/ExportRecord.php.html#11">Nzoom\Export\Entity\ExportRecord</a></td><td class="text-right">611</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#12">Nzoom\Export\Streamer\FileStreamer</a></td><td class="text-right">552</td></tr>
       <tr><td><a href="Entity/ExportHeader.php.html#11">Nzoom\Export\Entity\ExportHeader</a></td><td class="text-right">426</td></tr>
       <tr><td><a href="Streamer/GeneratorFileStreamer.php.html#11">Nzoom\Export\Streamer\GeneratorFileStreamer</a></td><td class="text-right">420</td></tr>
       <tr><td><a href="Streamer/PointerFileStreamer.php.html#11">Nzoom\Export\Streamer\PointerFileStreamer</a></td><td class="text-right">342</td></tr>
       <tr><td><a href="Entity/ExportColumn.php.html#10">Nzoom\Export\Entity\ExportColumn</a></td><td class="text-right">38</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Adapter/AbstractExportFormatAdapter.php.html#43"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportValue.php.html#131"><abbr title="Nzoom\Export\Entity\ExportValue::getFormat">getFormat</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportActionFactory.php.html#85"><abbr title="Nzoom\Export\ExportActionFactory::setModelName">setModelName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportActionFactory.php.html#55"><abbr title="Nzoom\Export\ExportActionFactory::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportValue.php.html#233"><abbr title="Nzoom\Export\Entity\ExportValue::__toString">__toString</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportValue.php.html#196"><abbr title="Nzoom\Export\Entity\ExportValue::getFormattedValue">getFormattedValue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportValue.php.html#153"><abbr title="Nzoom\Export\Entity\ExportValue::isNull">isNull</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportValue.php.html#142"><abbr title="Nzoom\Export\Entity\ExportValue::setFormat">setFormat</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportValue.php.html#102"><abbr title="Nzoom\Export\Entity\ExportValue::getType">getType</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportActionFactory.php.html#110"><abbr title="Nzoom\Export\ExportActionFactory::__invoke">__invoke</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportTableCollection.php.html#274"><abbr title="Nzoom\Export\Entity\ExportTableCollection::fromArray">fromArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportTableCollection.php.html#252"><abbr title="Nzoom\Export\Entity\ExportTableCollection::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportTableCollection.php.html#240"><abbr title="Nzoom\Export\Entity\ExportTableCollection::getIterator">getIterator</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportTableCollection.php.html#230"><abbr title="Nzoom\Export\Entity\ExportTableCollection::count">count</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportTableCollection.php.html#216"><abbr title="Nzoom\Export\Entity\ExportTableCollection::getTotalRecordCount">getTotalRecordCount</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportTableCollection.php.html#200"><abbr title="Nzoom\Export\Entity\ExportTableCollection::validate">validate</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportActionFactory.php.html#97"><abbr title="Nzoom\Export\ExportActionFactory::setModelFactoryName">setModelFactoryName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportActionFactory.php.html#122"><abbr title="Nzoom\Export\ExportActionFactory::createExportAction">createExportAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportTableCollection.php.html#179"><abbr title="Nzoom\Export\Entity\ExportTableCollection::getMetadataValue">getMetadataValue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportService.php.html#84"><abbr title="Nzoom\Export\ExportService::setModelName">setModelName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportService.php.html#181"><abbr title="Nzoom\Export\ExportService::createTempStream">createTempStream</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportService.php.html#156"><abbr title="Nzoom\Export\ExportService::export">export</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportService.php.html#142"><abbr title="Nzoom\Export\ExportService::createGeneratorFileStreamer">createGeneratorFileStreamer</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportService.php.html#127"><abbr title="Nzoom\Export\ExportService::createExportData">createExportData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportService.php.html#110"><abbr title="Nzoom\Export\ExportService::createExportAction">createExportAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportService.php.html#96"><abbr title="Nzoom\Export\ExportService::setModelFactoryName">setModelFactoryName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportService.php.html#69"><abbr title="Nzoom\Export\ExportService::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportActionFactory.php.html#141"><abbr title="Nzoom\Export\ExportActionFactory::prepareExportOptions">prepareExportOptions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportActionFactory.php.html#397"><abbr title="Nzoom\Export\ExportActionFactory::getDelimiterOptions">getDelimiterOptions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportActionFactory.php.html#364"><abbr title="Nzoom\Export\ExportActionFactory::getGroupTablesOptions">getGroupTablesOptions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportActionFactory.php.html#329"><abbr title="Nzoom\Export\ExportActionFactory::getFormatOptions">getFormatOptions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportActionFactory.php.html#280"><abbr title="Nzoom\Export\ExportActionFactory::buildBaseExportOptions">buildBaseExportOptions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportActionFactory.php.html#209"><abbr title="Nzoom\Export\ExportActionFactory::getPluginOptions">getPluginOptions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportActionFactory.php.html#194"><abbr title="Nzoom\Export\ExportActionFactory::firstOrZero">firstOrZero</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportActionFactory.php.html#177"><abbr title="Nzoom\Export\ExportActionFactory::initializeFilterVisibility">initializeFilterVisibility</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportTableCollection.php.html#190"><abbr title="Nzoom\Export\Entity\ExportTableCollection::setMetadataValue">setMetadataValue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportTableCollection.php.html#167"><abbr title="Nzoom\Export\Entity\ExportTableCollection::setMetadata">setMetadata</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportService.php.html#232"><abbr title="Nzoom\Export\ExportService::getFormatFactory">getFormatFactory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportRecord.php.html#225"><abbr title="Nzoom\Export\Entity\ExportRecord::setMetadataValue">setMetadataValue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportRecord.php.html#311"><abbr title="Nzoom\Export\Entity\ExportRecord::rewind">rewind</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportRecord.php.html#276"><abbr title="Nzoom\Export\Entity\ExportRecord::addTable">addTable</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportRecord.php.html#266"><abbr title="Nzoom\Export\Entity\ExportRecord::getTable">getTable</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportRecord.php.html#255"><abbr title="Nzoom\Export\Entity\ExportRecord::hasTables">hasTables</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportRecord.php.html#245"><abbr title="Nzoom\Export\Entity\ExportRecord::setTableCollection">setTableCollection</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportRecord.php.html#235"><abbr title="Nzoom\Export\Entity\ExportRecord::getTableCollection">getTableCollection</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportRecord.php.html#214"><abbr title="Nzoom\Export\Entity\ExportRecord::getMetadataValue">getMetadataValue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportRecord.php.html#331"><abbr title="Nzoom\Export\Entity\ExportRecord::key">key</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportRecord.php.html#202"><abbr title="Nzoom\Export\Entity\ExportRecord::setMetadata">setMetadata</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportRecord.php.html#192"><abbr title="Nzoom\Export\Entity\ExportRecord::getMetadata">getMetadata</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportRecord.php.html#180"><abbr title="Nzoom\Export\Entity\ExportRecord::getFormattedValues">getFormattedValues</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportRecord.php.html#158"><abbr title="Nzoom\Export\Entity\ExportRecord::hasValue">hasValue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportRecord.php.html#143"><abbr title="Nzoom\Export\Entity\ExportRecord::getValueByColumnName">getValueByColumnName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportRecord.php.html#121"><abbr title="Nzoom\Export\Entity\ExportRecord::getValues">getValues</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportRecord.php.html#103"><abbr title="Nzoom\Export\Entity\ExportRecord::setValueByColumnName">setValueByColumnName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportRecord.php.html#321"><abbr title="Nzoom\Export\Entity\ExportRecord::current">current</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportRecord.php.html#339"><abbr title="Nzoom\Export\Entity\ExportRecord::next">next</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportTableCollection.php.html#157"><abbr title="Nzoom\Export\Entity\ExportTableCollection::getMetadata">getMetadata</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportTableCollection.php.html#81"><abbr title="Nzoom\Export\Entity\ExportTableCollection::removeTable">removeTable</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportTableCollection.php.html#146"><abbr title="Nzoom\Export\Entity\ExportTableCollection::getTableTypesWithRecords">getTableTypesWithRecords</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportTableCollection.php.html#134"><abbr title="Nzoom\Export\Entity\ExportTableCollection::getTablesWithRecords">getTablesWithRecords</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportTableCollection.php.html#124"><abbr title="Nzoom\Export\Entity\ExportTableCollection::hasTables">hasTables</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportTableCollection.php.html#114"><abbr title="Nzoom\Export\Entity\ExportTableCollection::clearTables">clearTables</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportTableCollection.php.html#106"><abbr title="Nzoom\Export\Entity\ExportTableCollection::getTableTypes">getTableTypes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportTableCollection.php.html#96"><abbr title="Nzoom\Export\Entity\ExportTableCollection::getTables">getTables</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportTableCollection.php.html#70"><abbr title="Nzoom\Export\Entity\ExportTableCollection::hasTable">hasTable</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportRecord.php.html#349"><abbr title="Nzoom\Export\Entity\ExportRecord::valid">valid</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportTableCollection.php.html#59"><abbr title="Nzoom\Export\Entity\ExportTableCollection::getTable">getTable</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportTableCollection.php.html#39"><abbr title="Nzoom\Export\Entity\ExportTableCollection::addTable">addTable</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportTableCollection.php.html#28"><abbr title="Nzoom\Export\Entity\ExportTableCollection::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportTable.php.html#215"><abbr title="Nzoom\Export\Entity\ExportTable::setMetadata">setMetadata</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportTable.php.html#131"><abbr title="Nzoom\Export\Entity\ExportTable::setParentReference">setParentReference</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportTable.php.html#111"><abbr title="Nzoom\Export\Entity\ExportTable::setHeader">setHeader</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportTable.php.html#91"><abbr title="Nzoom\Export\Entity\ExportTable::setTableName">setTableName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportService.php.html#201"><abbr title="Nzoom\Export\ExportService::streamToBrowser">streamToBrowser</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportService.php.html#247"><abbr title="Nzoom\Export\ExportService::setFormatFactory">setFormatFactory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/AbstractExportFormatAdapter.php.html#57"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::setConfiguration">setConfiguration</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/GeneratorFileStreamer.php.html#36"><abbr title="Nzoom\Export\Streamer\GeneratorFileStreamer::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/GeneratorFileStreamer.php.html#150"><abbr title="Nzoom\Export\Streamer\GeneratorFileStreamer::reset">reset</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/GeneratorFileStreamer.php.html#140"><abbr title="Nzoom\Export\Streamer\GeneratorFileStreamer::getTotalSize">getTotalSize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/GeneratorFileStreamer.php.html#123"><abbr title="Nzoom\Export\Streamer\GeneratorFileStreamer::setTotalSize">setTotalSize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/GeneratorFileStreamer.php.html#98"><abbr title="Nzoom\Export\Streamer\GeneratorFileStreamer::cleanup">cleanup</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/GeneratorFileStreamer.php.html#68"><abbr title="Nzoom\Export\Streamer\GeneratorFileStreamer::performStreaming">performStreaming</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/GeneratorFileStreamer.php.html#56"><abbr title="Nzoom\Export\Streamer\GeneratorFileStreamer::initializeGenerator">initializeGenerator</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#288"><abbr title="Nzoom\Export\Streamer\FileStreamer::getMimeType">getMimeType</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/PointerFileStreamer.php.html#71"><abbr title="Nzoom\Export\Streamer\PointerFileStreamer::performStreaming">performStreaming</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#278"><abbr title="Nzoom\Export\Streamer\FileStreamer::getFilename">getFilename</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#265"><abbr title="Nzoom\Export\Streamer\FileStreamer::cleanup">cleanup</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#253"><abbr title="Nzoom\Export\Streamer\FileStreamer::increaseExecutionTime">increaseExecutionTime</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#243"><abbr title="Nzoom\Export\Streamer\FileStreamer::isClientConnected">isClientConnected</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#225"><abbr title="Nzoom\Export\Streamer\FileStreamer::outputChunk">outputChunk</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#200"><abbr title="Nzoom\Export\Streamer\FileStreamer::setStreamingOptimizationHeaders">setStreamingOptimizationHeaders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/PointerFileStreamer.php.html#36"><abbr title="Nzoom\Export\Streamer\PointerFileStreamer::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/PointerFileStreamer.php.html#93"><abbr title="Nzoom\Export\Streamer\PointerFileStreamer::cleanup">cleanup</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#142"><abbr title="Nzoom\Export\Streamer\FileStreamer::stream">stream</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/StreamHeaders.php.html#89"><abbr title="Nzoom\Export\Streamer\StreamHeaders::removeHeader">removeHeader</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/StreamHeaders.php.html#204"><abbr title="Nzoom\Export\Streamer\StreamHeaders::send304NotModified">send304NotModified</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/StreamHeaders.php.html#189"><abbr title="Nzoom\Export\Streamer\StreamHeaders::flushHeaders">flushHeaders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/StreamHeaders.php.html#168"><abbr title="Nzoom\Export\Streamer\StreamHeaders::sendPreparedHeaders">sendPreparedHeaders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/StreamHeaders.php.html#138"><abbr title="Nzoom\Export\Streamer\StreamHeaders::prepareCacheHeaders">prepareCacheHeaders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/StreamHeaders.php.html#117"><abbr title="Nzoom\Export\Streamer\StreamHeaders::sanitizeFilename">sanitizeFilename</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/StreamHeaders.php.html#101"><abbr title="Nzoom\Export\Streamer\StreamHeaders::setFileContentHeaders">setFileContentHeaders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/StreamHeaders.php.html#78"><abbr title="Nzoom\Export\Streamer\StreamHeaders::hasHeader">hasHeader</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/PointerFileStreamer.php.html#110"><abbr title="Nzoom\Export\Streamer\PointerFileStreamer::getChunkSize">getChunkSize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/StreamHeaders.php.html#67"><abbr title="Nzoom\Export\Streamer\StreamHeaders::clear">clear</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/StreamHeaders.php.html#57"><abbr title="Nzoom\Export\Streamer\StreamHeaders::getAll">getAll</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/StreamHeaders.php.html#47"><abbr title="Nzoom\Export\Streamer\StreamHeaders::getHeader">getHeader</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/StreamHeaders.php.html#36"><abbr title="Nzoom\Export\Streamer\StreamHeaders::setHeaders">setHeaders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/StreamHeaders.php.html#25"><abbr title="Nzoom\Export\Streamer\StreamHeaders::addHeader">addHeader</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/PointerFileStreamer.php.html#136"><abbr title="Nzoom\Export\Streamer\PointerFileStreamer::getTotalSize">getTotalSize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/PointerFileStreamer.php.html#121"><abbr title="Nzoom\Export\Streamer\PointerFileStreamer::setChunkSize">setChunkSize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#175"><abbr title="Nzoom\Export\Streamer\FileStreamer::prepareForStreaming">prepareForStreaming</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#129"><abbr title="Nzoom\Export\Streamer\FileStreamer::setLastModified">setLastModified</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportService.php.html#259"><abbr title="Nzoom\Export\ExportService::getAdapter">getAdapter</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Factory/ExportFormatFactory.php.html#148"><abbr title="Nzoom\Export\Factory\ExportFormatFactory::getClassNameFromFile">getClassNameFromFile</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Provider/ModelTableProvider.php.html#52"><abbr title="Nzoom\Export\Provider\ModelTableProvider::getDefaultOptions">getDefaultOptions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Provider/ModelTableProvider.php.html#41"><abbr title="Nzoom\Export\Provider\ModelTableProvider::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Factory/ExportFormatFactory.php.html#227"><abbr title="Nzoom\Export\Factory\ExportFormatFactory::createAdapterFromFilename">createAdapterFromFilename</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Factory/ExportFormatFactory.php.html#213"><abbr title="Nzoom\Export\Factory\ExportFormatFactory::isFormatSupported">isFormatSupported</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Factory/ExportFormatFactory.php.html#186"><abbr title="Nzoom\Export\Factory\ExportFormatFactory::getSupportedFormats">getSupportedFormats</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Factory/ExportFormatFactory.php.html#171"><abbr title="Nzoom\Export\Factory\ExportFormatFactory::isValidAdapter">isValidAdapter</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Factory/ExportFormatFactory.php.html#112"><abbr title="Nzoom\Export\Factory\ExportFormatFactory::discoverAdapters">discoverAdapters</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Provider/ModelTableProvider.php.html#103"><abbr title="Nzoom\Export\Provider\ModelTableProvider::discoverGroupingVariables">discoverGroupingVariables</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Factory/ExportFormatFactory.php.html#94"><abbr title="Nzoom\Export\Factory\ExportFormatFactory::getAdapterClass">getAdapterClass</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Factory/ExportFormatFactory.php.html#60"><abbr title="Nzoom\Export\Factory\ExportFormatFactory::createAdapter">createAdapter</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Factory/ExportFormatFactory.php.html#44"><abbr title="Nzoom\Export\Factory\ExportFormatFactory::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportService.php.html#326"><abbr title="Nzoom\Export\ExportService::handleExportError">handleExportError</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportService.php.html#296"><abbr title="Nzoom\Export\ExportService::getExportFilename">getExportFilename</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportService.php.html#284"><abbr title="Nzoom\Export\ExportService::isFormatSupported">isFormatSupported</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportService.php.html#273"><abbr title="Nzoom\Export\ExportService::getSupportedFormats">getSupportedFormats</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Provider/ModelTableProvider.php.html#65"><abbr title="Nzoom\Export\Provider\ModelTableProvider::getTablesForRecord">getTablesForRecord</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Provider/ModelTableProvider.php.html#135"><abbr title="Nzoom\Export\Provider\ModelTableProvider::getModelVariables">getModelVariables</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#118"><abbr title="Nzoom\Export\Streamer\FileStreamer::setETag">setETag</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Provider/ModelTableProvider.php.html#407"><abbr title="Nzoom\Export\Provider\ModelTableProvider::getTableConfiguration">getTableConfiguration</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#107"><abbr title="Nzoom\Export\Streamer\FileStreamer::setCacheExpires">setCacheExpires</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#96"><abbr title="Nzoom\Export\Streamer\FileStreamer::setTimeIncrement">setTimeIncrement</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#85"><abbr title="Nzoom\Export\Streamer\FileStreamer::setHeaders">setHeaders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#71"><abbr title="Nzoom\Export\Streamer\FileStreamer::getHeaders">getHeaders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#60"><abbr title="Nzoom\Export\Streamer\FileStreamer::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Provider/ModelTableProvider.php.html#419"><abbr title="Nzoom\Export\Provider\ModelTableProvider::validateRecord">validateRecord</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Provider/ModelTableProvider.php.html#398"><abbr title="Nzoom\Export\Provider\ModelTableProvider::supportsTableType">supportsTableType</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Provider/ModelTableProvider.php.html#160"><abbr title="Nzoom\Export\Provider\ModelTableProvider::createTableFromGroupingData">createTableFromGroupingData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Provider/ModelTableProvider.php.html#388"><abbr title="Nzoom\Export\Provider\ModelTableProvider::getSupportedTableTypes">getSupportedTableTypes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Provider/ModelTableProvider.php.html#349"><abbr title="Nzoom\Export\Provider\ModelTableProvider::formatValue">formatValue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Provider/ModelTableProvider.php.html#317"><abbr title="Nzoom\Export\Provider\ModelTableProvider::createRecordFromRowData">createRecordFromRowData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Provider/ModelTableProvider.php.html#290"><abbr title="Nzoom\Export\Provider\ModelTableProvider::populateTableFromGroupingData">populateTableFromGroupingData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Provider/ModelTableProvider.php.html#252"><abbr title="Nzoom\Export\Provider\ModelTableProvider::guessColumnType">guessColumnType</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Provider/ModelTableProvider.php.html#237"><abbr title="Nzoom\Export\Provider\ModelTableProvider::formatTableName">formatTableName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Provider/ModelTableProvider.php.html#202"><abbr title="Nzoom\Export\Provider\ModelTableProvider::getOrCreateTableHeader">getOrCreateTableHeader</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportRecord.php.html#76"><abbr title="Nzoom\Export\Entity\ExportRecord::setValueAt">setValueAt</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportHeader.php.html#329"><abbr title="Nzoom\Export\Entity\ExportHeader::valid">valid</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportHeader.php.html#319"><abbr title="Nzoom\Export\Entity\ExportHeader::next">next</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#599"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::convertCustomDateFormat">convertCustomDateFormat</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#785"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::processExportTables">processExportTables</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#766"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::applyVerticalAlignment">applyVerticalAlignment</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#709"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::applyRowHeightConstraints">applyRowHeightConstraints</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#672"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::applyColumnWidthConstraints">applyColumnWidthConstraints</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#648"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::finalizeExportDataColumns">finalizeExportDataColumns</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#630"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::handleExportRecordCellError">handleExportRecordCellError</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#576"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::convertCustomNumberFormat">convertCustomNumberFormat</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#839"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::createTableWorksheet">createTableWorksheet</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#525"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::getExcelFormatFromExportValue">getExcelFormatFromExportValue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#497"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::setCellDateValue">setCellDateValue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#436"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::setCellValueWithFormatting">setCellValueWithFormatting</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#411"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::processExportRecord">processExportRecord</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#362"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::processExportDataRecords">processExportDataRecords</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#346"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::styleHeaderRow">styleHeaderRow</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#310"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::addNamedRanges">addNamedRanges</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#809"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::collectTablesByType">collectTablesByType</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#867"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::sanitizeWorksheetName">sanitizeWorksheetName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#265"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::processExportData">processExportData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#1159"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::getDefaultExtension">getDefaultExtension</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/JsonExportFormatAdapter.php.html#89"><abbr title="Nzoom\Export\Adapter\JsonExportFormatAdapter::writeJsonContent">writeJsonContent</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/JsonExportFormatAdapter.php.html#65"><abbr title="Nzoom\Export\Adapter\JsonExportFormatAdapter::extractJsonOptions">extractJsonOptions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/JsonExportFormatAdapter.php.html#30"><abbr title="Nzoom\Export\Adapter\JsonExportFormatAdapter::export">export</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#1183"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::getFormatOptions">getFormatOptions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#1175"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::getFormatName">getFormatName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#1167"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::supportsFormat">supportsFormat</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#1142"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::getMimeType">getMimeType</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#881"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::populateTableWorksheet">populateTableWorksheet</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#1134"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::getSupportedExtensions">getSupportedExtensions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#1111"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::convertToBytes">convertToBytes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#1088"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::optimizeMemoryForExport">optimizeMemoryForExport</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#986"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::getExcelFormatting">getExcelFormatting</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#956"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::handleSpreadsheetError">handleSpreadsheetError</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#935"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::createWriter">createWriter</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#915"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::addTableDataToWorksheet">addTableDataToWorksheet</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#295"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::addHeaders">addHeaders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#244"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::setDocumentProperties">setDocumentProperties</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/JsonExportFormatAdapter.php.html#148"><abbr title="Nzoom\Export\Adapter\JsonExportFormatAdapter::prepareArrayStructure">prepareArrayStructure</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/AbstractExportFormatAdapter.php.html#273"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::validateFilePath">validateFilePath</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/CsvExportFormatAdapter.php.html#209"><abbr title="Nzoom\Export\Adapter\CsvExportFormatAdapter::processExportRecord">processExportRecord</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/CsvExportFormatAdapter.php.html#173"><abbr title="Nzoom\Export\Adapter\CsvExportFormatAdapter::processExportDataRecords">processExportDataRecords</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/CsvExportFormatAdapter.php.html#128"><abbr title="Nzoom\Export\Adapter\CsvExportFormatAdapter::writeCsvContent">writeCsvContent</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/CsvExportFormatAdapter.php.html#85"><abbr title="Nzoom\Export\Adapter\CsvExportFormatAdapter::extractCsvOptions">extractCsvOptions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/CsvExportFormatAdapter.php.html#50"><abbr title="Nzoom\Export\Adapter\CsvExportFormatAdapter::export">export</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/AbstractExportFormatAdapter.php.html#295"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::validateFilePointer">validateFilePointer</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/AbstractExportFormatAdapter.php.html#231"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::validatePhpStreamWrapper">validatePhpStreamWrapper</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/CsvExportFormatAdapter.php.html#271"><abbr title="Nzoom\Export\Adapter\CsvExportFormatAdapter::formatDateValue">formatDateValue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/AbstractExportFormatAdapter.php.html#213"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::validateStringTarget">validateStringTarget</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/AbstractExportFormatAdapter.php.html#193"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::validateAndPrepareSaveTarget">validateAndPrepareSaveTarget</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/AbstractExportFormatAdapter.php.html#181"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::getFormatOptions">getFormatOptions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/AbstractExportFormatAdapter.php.html#161"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::getRecordHeaders">getRecordHeaders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/AbstractExportFormatAdapter.php.html#127"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::handleExportError">handleExportError</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/AbstractExportFormatAdapter.php.html#100"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::sendHeaders">sendHeaders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/AbstractExportFormatAdapter.php.html#70"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::getExportFilename">getExportFilename</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/CsvExportFormatAdapter.php.html#227"><abbr title="Nzoom\Export\Adapter\CsvExportFormatAdapter::formatValueForCsv">formatValueForCsv</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/CsvExportFormatAdapter.php.html#296"><abbr title="Nzoom\Export\Adapter\CsvExportFormatAdapter::getDecimalPlaces">getDecimalPlaces</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#193"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::getApplicationLocale">getApplicationLocale</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/CsvExportFormatAdapter.php.html#424"><abbr title="Nzoom\Export\Adapter\CsvExportFormatAdapter::supportsFormat">supportsFormat</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#170"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::setSpreadsheetLocale">setSpreadsheetLocale</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#138"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::createSpreadsheet">createSpreadsheet</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#106"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::extractSizingOptions">extractSizingOptions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#56"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::export">export</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/CsvExportFormatAdapter.php.html#440"><abbr title="Nzoom\Export\Adapter\CsvExportFormatAdapter::getFormatOptions">getFormatOptions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/CsvExportFormatAdapter.php.html#432"><abbr title="Nzoom\Export\Adapter\CsvExportFormatAdapter::getFormatName">getFormatName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/CsvExportFormatAdapter.php.html#416"><abbr title="Nzoom\Export\Adapter\CsvExportFormatAdapter::getDefaultExtension">getDefaultExtension</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/CsvExportFormatAdapter.php.html#312"><abbr title="Nzoom\Export\Adapter\CsvExportFormatAdapter::getDelimiter">getDelimiter</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/CsvExportFormatAdapter.php.html#407"><abbr title="Nzoom\Export\Adapter\CsvExportFormatAdapter::getMimeType">getMimeType</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/CsvExportFormatAdapter.php.html#399"><abbr title="Nzoom\Export\Adapter\CsvExportFormatAdapter::getSupportedExtensions">getSupportedExtensions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/CsvExportFormatAdapter.php.html#391"><abbr title="Nzoom\Export\Adapter\CsvExportFormatAdapter::getDatetimeFormat">getDatetimeFormat</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/CsvExportFormatAdapter.php.html#381"><abbr title="Nzoom\Export\Adapter\CsvExportFormatAdapter::getDateFormat">getDateFormat</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/CsvExportFormatAdapter.php.html#371"><abbr title="Nzoom\Export\Adapter\CsvExportFormatAdapter::setDatetimeFormat">setDatetimeFormat</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/CsvExportFormatAdapter.php.html#360"><abbr title="Nzoom\Export\Adapter\CsvExportFormatAdapter::setDateFormat">setDateFormat</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/CsvExportFormatAdapter.php.html#339"><abbr title="Nzoom\Export\Adapter\CsvExportFormatAdapter::normalizeDelimiter">normalizeDelimiter</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/JsonExportFormatAdapter.php.html#129"><abbr title="Nzoom\Export\Adapter\JsonExportFormatAdapter::prepareJsonData">prepareJsonData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/JsonExportFormatAdapter.php.html#176"><abbr title="Nzoom\Export\Adapter\JsonExportFormatAdapter::prepareObjectStructure">prepareObjectStructure</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportHeader.php.html#311"><abbr title="Nzoom\Export\Entity\ExportHeader::key">key</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportData.php.html#168"><abbr title="Nzoom\Export\Entity\ExportData::setMetadataValue">setMetadataValue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportData.php.html#277"><abbr title="Nzoom\Export\Entity\ExportData::isEmpty">isEmpty</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportData.php.html#261"><abbr title="Nzoom\Export\Entity\ExportData::createRecord">createRecord</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportData.php.html#244"><abbr title="Nzoom\Export\Entity\ExportData::count">count</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportData.php.html#222"><abbr title="Nzoom\Export\Entity\ExportData::getRecordAt">getRecordAt</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportData.php.html#201"><abbr title="Nzoom\Export\Entity\ExportData::getRecords">getRecords</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportData.php.html#182"><abbr title="Nzoom\Export\Entity\ExportData::addRecord">addRecord</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportData.php.html#157"><abbr title="Nzoom\Export\Entity\ExportData::getMetadataValue">getMetadataValue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportData.php.html#341"><abbr title="Nzoom\Export\Entity\ExportData::filter">filter</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportData.php.html#145"><abbr title="Nzoom\Export\Entity\ExportData::setMetadata">setMetadata</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportData.php.html#135"><abbr title="Nzoom\Export\Entity\ExportData::getMetadata">getMetadata</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportData.php.html#125"><abbr title="Nzoom\Export\Entity\ExportData::setHeader">setHeader</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportData.php.html#115"><abbr title="Nzoom\Export\Entity\ExportData::getHeader">getHeader</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportData.php.html#104"><abbr title="Nzoom\Export\Entity\ExportData::setPageSize">setPageSize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportData.php.html#93"><abbr title="Nzoom\Export\Entity\ExportData::getPageSize">getPageSize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportData.php.html#293"><abbr title="Nzoom\Export\Entity\ExportData::sortByColumn">sortByColumn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportData.php.html#352"><abbr title="Nzoom\Export\Entity\ExportData::validate">validate</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportData.php.html#66"><abbr title="Nzoom\Export\Entity\ExportData::setRecordProvider">setRecordProvider</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportHeader.php.html#155"><abbr title="Nzoom\Export\Entity\ExportHeader::setStyles">setStyles</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportHeader.php.html#301"><abbr title="Nzoom\Export\Entity\ExportHeader::current">current</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportHeader.php.html#291"><abbr title="Nzoom\Export\Entity\ExportHeader::rewind">rewind</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportHeader.php.html#223"><abbr title="Nzoom\Export\Entity\ExportHeader::reorderColumns">reorderColumns</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportHeader.php.html#210"><abbr title="Nzoom\Export\Entity\ExportHeader::getTypes">getTypes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportHeader.php.html#198"><abbr title="Nzoom\Export\Entity\ExportHeader::getVarNames">getVarNames</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportHeader.php.html#166"><abbr title="Nzoom\Export\Entity\ExportHeader::addStyle">addStyle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportHeader.php.html#145"><abbr title="Nzoom\Export\Entity\ExportHeader::getStyles">getStyles</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportData.php.html#370"><abbr title="Nzoom\Export\Entity\ExportData::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportHeader.php.html#135"><abbr title="Nzoom\Export\Entity\ExportHeader::setBackgroundColor">setBackgroundColor</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportHeader.php.html#125"><abbr title="Nzoom\Export\Entity\ExportHeader::getBackgroundColor">getBackgroundColor</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportHeader.php.html#111"><abbr title="Nzoom\Export\Entity\ExportHeader::getColumnByVarName">getColumnByVarName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportHeader.php.html#100"><abbr title="Nzoom\Export\Entity\ExportHeader::getColumnAt">getColumnAt</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportHeader.php.html#89"><abbr title="Nzoom\Export\Entity\ExportHeader::getColumns">getColumns</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportData.php.html#406"><abbr title="Nzoom\Export\Entity\ExportData::getLazyIterator">getLazyIterator</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportData.php.html#390"><abbr title="Nzoom\Export\Entity\ExportData::getIterator">getIterator</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportData.php.html#83"><abbr title="Nzoom\Export\Entity\ExportData::isLazy">isLazy</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportData.php.html#52"><abbr title="Nzoom\Export\Entity\ExportData::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/JsonExportFormatAdapter.php.html#201"><abbr title="Nzoom\Export\Adapter\JsonExportFormatAdapter::prepareNestedStructure">prepareNestedStructure</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/JsonExportFormatAdapter.php.html#445"><abbr title="Nzoom\Export\Adapter\JsonExportFormatAdapter::getDefaultExtension">getDefaultExtension</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DataFactory.php.html#59"><abbr title="Nzoom\Export\DataFactory::isTablesEnabled">isTablesEnabled</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DataFactory.php.html#49"><abbr title="Nzoom\Export\DataFactory::setTableProvider">setTableProvider</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DataFactory.php.html#39"><abbr title="Nzoom\Export\DataFactory::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/JsonExportFormatAdapter.php.html#469"><abbr title="Nzoom\Export\Adapter\JsonExportFormatAdapter::getFormatOptions">getFormatOptions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/JsonExportFormatAdapter.php.html#461"><abbr title="Nzoom\Export\Adapter\JsonExportFormatAdapter::getFormatName">getFormatName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/JsonExportFormatAdapter.php.html#453"><abbr title="Nzoom\Export\Adapter\JsonExportFormatAdapter::supportsFormat">supportsFormat</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/JsonExportFormatAdapter.php.html#436"><abbr title="Nzoom\Export\Adapter\JsonExportFormatAdapter::getMimeType">getMimeType</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportColumn.php.html#235"><abbr title="Nzoom\Export\Entity\ExportColumn::createValue">createValue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/JsonExportFormatAdapter.php.html#428"><abbr title="Nzoom\Export\Adapter\JsonExportFormatAdapter::getSupportedExtensions">getSupportedExtensions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/JsonExportFormatAdapter.php.html#373"><abbr title="Nzoom\Export\Adapter\JsonExportFormatAdapter::getJsonOptions">getJsonOptions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/JsonExportFormatAdapter.php.html#338"><abbr title="Nzoom\Export\Adapter\JsonExportFormatAdapter::getMetadata">getMetadata</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/JsonExportFormatAdapter.php.html#324"><abbr title="Nzoom\Export\Adapter\JsonExportFormatAdapter::addMetadata">addMetadata</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/JsonExportFormatAdapter.php.html#297"><abbr title="Nzoom\Export\Adapter\JsonExportFormatAdapter::formatDateValue">formatDateValue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/JsonExportFormatAdapter.php.html#259"><abbr title="Nzoom\Export\Adapter\JsonExportFormatAdapter::formatValueForJson">formatValueForJson</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/JsonExportFormatAdapter.php.html#233"><abbr title="Nzoom\Export\Adapter\JsonExportFormatAdapter::processExportRecord">processExportRecord</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DataFactory.php.html#70"><abbr title="Nzoom\Export\DataFactory::withModelTableProvider">withModelTableProvider</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DataFactory.php.html#84"><abbr title="Nzoom\Export\DataFactory::__invoke">__invoke</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DataFactory.php.html#112"><abbr title="Nzoom\Export\DataFactory::createHeaderFromOutlook">createHeaderFromOutlook</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DataFactory.php.html#147"><abbr title="Nzoom\Export\DataFactory::processModelsChunk">processModelsChunk</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportColumn.php.html#212"><abbr title="Nzoom\Export\Entity\ExportColumn::addStyle">addStyle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportColumn.php.html#191"><abbr title="Nzoom\Export\Entity\ExportColumn::getStyles">getStyles</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportColumn.php.html#171"><abbr title="Nzoom\Export\Entity\ExportColumn::getWidth">getWidth</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportColumn.php.html#150"><abbr title="Nzoom\Export\Entity\ExportColumn::getFormat">getFormat</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportColumn.php.html#120"><abbr title="Nzoom\Export\Entity\ExportColumn::getType">getType</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DataFactory.php.html#318"><abbr title="Nzoom\Export\DataFactory::createCursorStreaming">createCursorStreaming</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DataFactory.php.html#269"><abbr title="Nzoom\Export\DataFactory::createStreaming">createStreaming</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DataFactory.php.html#255"><abbr title="Nzoom\Export\DataFactory::setChunkSize">setChunkSize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DataFactory.php.html#226"><abbr title="Nzoom\Export\DataFactory::mapFieldTypeToExportType">mapFieldTypeToExportType</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DataFactory.php.html#202"><abbr title="Nzoom\Export\DataFactory::extractTablesForRecord">extractTablesForRecord</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DataFactory.php.html#161"><abbr title="Nzoom\Export\DataFactory::createRecordFromModel">createRecordFromModel</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/StreamHeaders.php.html#223"><abbr title="Nzoom\Export\Streamer\StreamHeaders::handleCacheValidation">handleCacheValidation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportValue.php.html#113"><abbr title="Nzoom\Export\Entity\ExportValue::setType">setType</abbr></a></td><td class="text-right">28%</td></tr>
       <tr><td><a href="Entity/ExportColumn.php.html#131"><abbr title="Nzoom\Export\Entity\ExportColumn::setType">setType</abbr></a></td><td class="text-right">28%</td></tr>
       <tr><td><a href="Entity/ExportValue.php.html#163"><abbr title="Nzoom\Export\Entity\ExportValue::validate">validate</abbr></a></td><td class="text-right">35%</td></tr>
       <tr><td><a href="Entity/ExportHeader.php.html#57"><abbr title="Nzoom\Export\Entity\ExportHeader::addColumn">addColumn</abbr></a></td><td class="text-right">50%</td></tr>
       <tr><td><a href="Entity/ExportColumn.php.html#86"><abbr title="Nzoom\Export\Entity\ExportColumn::setVarName">setVarName</abbr></a></td><td class="text-right">66%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#986"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::getExcelFormatting">getExcelFormatting</abbr></a></td><td class="text-right">6972</td></tr>
       <tr><td><a href="Adapter/CsvExportFormatAdapter.php.html#85"><abbr title="Nzoom\Export\Adapter\CsvExportFormatAdapter::extractCsvOptions">extractCsvOptions</abbr></a></td><td class="text-right">380</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#436"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::setCellValueWithFormatting">setCellValueWithFormatting</abbr></a></td><td class="text-right">306</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#525"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::getExcelFormatFromExportValue">getExcelFormatFromExportValue</abbr></a></td><td class="text-right">240</td></tr>
       <tr><td><a href="Entity/ExportValue.php.html#196"><abbr title="Nzoom\Export\Entity\ExportValue::getFormattedValue">getFormattedValue</abbr></a></td><td class="text-right">240</td></tr>
       <tr><td><a href="Entity/ExportData.php.html#293"><abbr title="Nzoom\Export\Entity\ExportData::sortByColumn">sortByColumn</abbr></a></td><td class="text-right">240</td></tr>
       <tr><td><a href="Entity/ExportValue.php.html#163"><abbr title="Nzoom\Export\Entity\ExportValue::validate">validate</abbr></a></td><td class="text-right">227</td></tr>
       <tr><td><a href="Provider/ModelTableProvider.php.html#349"><abbr title="Nzoom\Export\Provider\ModelTableProvider::formatValue">formatValue</abbr></a></td><td class="text-right">182</td></tr>
       <tr><td><a href="Adapter/CsvExportFormatAdapter.php.html#227"><abbr title="Nzoom\Export\Adapter\CsvExportFormatAdapter::formatValueForCsv">formatValueForCsv</abbr></a></td><td class="text-right">156</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#106"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::extractSizingOptions">extractSizingOptions</abbr></a></td><td class="text-right">132</td></tr>
       <tr><td><a href="Adapter/JsonExportFormatAdapter.php.html#259"><abbr title="Nzoom\Export\Adapter\JsonExportFormatAdapter::formatValueForJson">formatValueForJson</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#709"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::applyRowHeightConstraints">applyRowHeightConstraints</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#362"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::processExportDataRecords">processExportDataRecords</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Streamer/GeneratorFileStreamer.php.html#68"><abbr title="Nzoom\Export\Streamer\GeneratorFileStreamer::performStreaming">performStreaming</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Entity/ExportValue.php.html#233"><abbr title="Nzoom\Export\Entity\ExportValue::__toString">__toString</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Provider/ModelTableProvider.php.html#65"><abbr title="Nzoom\Export\Provider\ModelTableProvider::getTablesForRecord">getTablesForRecord</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Adapter/JsonExportFormatAdapter.php.html#65"><abbr title="Nzoom\Export\Adapter\JsonExportFormatAdapter::extractJsonOptions">extractJsonOptions</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Adapter/JsonExportFormatAdapter.php.html#297"><abbr title="Nzoom\Export\Adapter\JsonExportFormatAdapter::formatDateValue">formatDateValue</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Adapter/JsonExportFormatAdapter.php.html#373"><abbr title="Nzoom\Export\Adapter\JsonExportFormatAdapter::getJsonOptions">getJsonOptions</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Streamer/StreamHeaders.php.html#223"><abbr title="Nzoom\Export\Streamer\StreamHeaders::handleCacheValidation">handleCacheValidation</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Adapter/CsvExportFormatAdapter.php.html#173"><abbr title="Nzoom\Export\Adapter\CsvExportFormatAdapter::processExportDataRecords">processExportDataRecords</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Adapter/CsvExportFormatAdapter.php.html#271"><abbr title="Nzoom\Export\Adapter\CsvExportFormatAdapter::formatDateValue">formatDateValue</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="DataFactory.php.html#112"><abbr title="Nzoom\Export\DataFactory::createHeaderFromOutlook">createHeaderFromOutlook</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Streamer/PointerFileStreamer.php.html#36"><abbr title="Nzoom\Export\Streamer\PointerFileStreamer::__construct">__construct</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Provider/ModelTableProvider.php.html#252"><abbr title="Nzoom\Export\Provider\ModelTableProvider::guessColumnType">guessColumnType</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Streamer/PointerFileStreamer.php.html#71"><abbr title="Nzoom\Export\Streamer\PointerFileStreamer::performStreaming">performStreaming</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Provider/ModelTableProvider.php.html#103"><abbr title="Nzoom\Export\Provider\ModelTableProvider::discoverGroupingVariables">discoverGroupingVariables</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Entity/ExportTableCollection.php.html#274"><abbr title="Nzoom\Export\Entity\ExportTableCollection::fromArray">fromArray</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Adapter/CsvExportFormatAdapter.php.html#128"><abbr title="Nzoom\Export\Adapter\CsvExportFormatAdapter::writeCsvContent">writeCsvContent</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Adapter/JsonExportFormatAdapter.php.html#89"><abbr title="Nzoom\Export\Adapter\JsonExportFormatAdapter::writeJsonContent">writeJsonContent</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Adapter/CsvExportFormatAdapter.php.html#339"><abbr title="Nzoom\Export\Adapter\CsvExportFormatAdapter::normalizeDelimiter">normalizeDelimiter</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Adapter/JsonExportFormatAdapter.php.html#201"><abbr title="Nzoom\Export\Adapter\JsonExportFormatAdapter::prepareNestedStructure">prepareNestedStructure</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Factory/ExportFormatFactory.php.html#112"><abbr title="Nzoom\Export\Factory\ExportFormatFactory::discoverAdapters">discoverAdapters</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="DataFactory.php.html#161"><abbr title="Nzoom\Export\DataFactory::createRecordFromModel">createRecordFromModel</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Entity/ExportHeader.php.html#223"><abbr title="Nzoom\Export\Entity\ExportHeader::reorderColumns">reorderColumns</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Adapter/JsonExportFormatAdapter.php.html#129"><abbr title="Nzoom\Export\Adapter\JsonExportFormatAdapter::prepareJsonData">prepareJsonData</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Adapter/JsonExportFormatAdapter.php.html#148"><abbr title="Nzoom\Export\Adapter\JsonExportFormatAdapter::prepareArrayStructure">prepareArrayStructure</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Adapter/AbstractExportFormatAdapter.php.html#70"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::getExportFilename">getExportFilename</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Adapter/AbstractExportFormatAdapter.php.html#231"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::validatePhpStreamWrapper">validatePhpStreamWrapper</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="DataFactory.php.html#202"><abbr title="Nzoom\Export\DataFactory::extractTablesForRecord">extractTablesForRecord</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#56"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::export">export</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#809"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::collectTablesByType">collectTablesByType</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Provider/ModelTableProvider.php.html#202"><abbr title="Nzoom\Export\Provider\ModelTableProvider::getOrCreateTableHeader">getOrCreateTableHeader</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Adapter/AbstractExportFormatAdapter.php.html#161"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::getRecordHeaders">getRecordHeaders</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#672"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::applyColumnWidthConstraints">applyColumnWidthConstraints</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#497"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::setCellDateValue">setCellDateValue</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Provider/ModelTableProvider.php.html#290"><abbr title="Nzoom\Export\Provider\ModelTableProvider::populateTableFromGroupingData">populateTableFromGroupingData</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Entity/ExportData.php.html#406"><abbr title="Nzoom\Export\Entity\ExportData::getLazyIterator">getLazyIterator</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Entity/ExportData.php.html#222"><abbr title="Nzoom\Export\Entity\ExportData::getRecordAt">getRecordAt</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Entity/ExportData.php.html#182"><abbr title="Nzoom\Export\Entity\ExportData::addRecord">addRecord</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Provider/ModelTableProvider.php.html#317"><abbr title="Nzoom\Export\Provider\ModelTableProvider::createRecordFromRowData">createRecordFromRowData</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Streamer/GeneratorFileStreamer.php.html#98"><abbr title="Nzoom\Export\Streamer\GeneratorFileStreamer::cleanup">cleanup</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Adapter/AbstractExportFormatAdapter.php.html#127"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::handleExportError">handleExportError</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ExportActionFactory.php.html#209"><abbr title="Nzoom\Export\ExportActionFactory::getPluginOptions">getPluginOptions</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ExportActionFactory.php.html#280"><abbr title="Nzoom\Export\ExportActionFactory::buildBaseExportOptions">buildBaseExportOptions</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Factory/ExportFormatFactory.php.html#148"><abbr title="Nzoom\Export\Factory\ExportFormatFactory::getClassNameFromFile">getClassNameFromFile</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ExportService.php.html#296"><abbr title="Nzoom\Export\ExportService::getExportFilename">getExportFilename</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ExportService.php.html#326"><abbr title="Nzoom\Export\ExportService::handleExportError">handleExportError</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Entity/ExportRecord.php.html#76"><abbr title="Nzoom\Export\Entity\ExportRecord::setValueAt">setValueAt</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="DataFactory.php.html#318"><abbr title="Nzoom\Export\DataFactory::createCursorStreaming">createCursorStreaming</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Adapter/AbstractExportFormatAdapter.php.html#295"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::validateFilePointer">validateFilePointer</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Adapter/JsonExportFormatAdapter.php.html#30"><abbr title="Nzoom\Export\Adapter\JsonExportFormatAdapter::export">export</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#935"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::createWriter">createWriter</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#956"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::handleSpreadsheetError">handleSpreadsheetError</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Streamer/StreamHeaders.php.html#138"><abbr title="Nzoom\Export\Streamer\StreamHeaders::prepareCacheHeaders">prepareCacheHeaders</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#1111"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::convertToBytes">convertToBytes</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#839"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::createTableWorksheet">createTableWorksheet</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#1142"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::getMimeType">getMimeType</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#785"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::processExportTables">processExportTables</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Adapter/CsvExportFormatAdapter.php.html#312"><abbr title="Nzoom\Export\Adapter\CsvExportFormatAdapter::getDelimiter">getDelimiter</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#310"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::addNamedRanges">addNamedRanges</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Adapter/CsvExportFormatAdapter.php.html#50"><abbr title="Nzoom\Export\Adapter\CsvExportFormatAdapter::export">export</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#170"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::setSpreadsheetLocale">setSpreadsheetLocale</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#193"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::getApplicationLocale">getApplicationLocale</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#766"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::applyVerticalAlignment">applyVerticalAlignment</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Streamer/StreamHeaders.php.html#168"><abbr title="Nzoom\Export\Streamer\StreamHeaders::sendPreparedHeaders">sendPreparedHeaders</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Entity/ExportTableCollection.php.html#200"><abbr title="Nzoom\Export\Entity\ExportTableCollection::validate">validate</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Provider/ModelTableProvider.php.html#160"><abbr title="Nzoom\Export\Provider\ModelTableProvider::createTableFromGroupingData">createTableFromGroupingData</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Adapter/AbstractExportFormatAdapter.php.html#193"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::validateAndPrepareSaveTarget">validateAndPrepareSaveTarget</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#576"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::convertCustomNumberFormat">convertCustomNumberFormat</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#411"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::processExportRecord">processExportRecord</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ExportService.php.html#201"><abbr title="Nzoom\Export\ExportService::streamToBrowser">streamToBrowser</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ExportActionFactory.php.html#55"><abbr title="Nzoom\Export\ExportActionFactory::__construct">__construct</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ExportService.php.html#156"><abbr title="Nzoom\Export\ExportService::export">export</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Factory/ExportFormatFactory.php.html#186"><abbr title="Nzoom\Export\Factory\ExportFormatFactory::getSupportedFormats">getSupportedFormats</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Adapter/AbstractExportFormatAdapter.php.html#100"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::sendHeaders">sendHeaders</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ExportActionFactory.php.html#177"><abbr title="Nzoom\Export\ExportActionFactory::initializeFilterVisibility">initializeFilterVisibility</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Factory/ExportFormatFactory.php.html#60"><abbr title="Nzoom\Export\Factory\ExportFormatFactory::createAdapter">createAdapter</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Adapter/JsonExportFormatAdapter.php.html#338"><abbr title="Nzoom\Export\Adapter\JsonExportFormatAdapter::getMetadata">getMetadata</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Adapter/AbstractExportFormatAdapter.php.html#273"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::validateFilePath">validateFilePath</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#881"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::populateTableWorksheet">populateTableWorksheet</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Factory/ExportFormatFactory.php.html#94"><abbr title="Nzoom\Export\Factory\ExportFormatFactory::getAdapterClass">getAdapterClass</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Adapter/JsonExportFormatAdapter.php.html#233"><abbr title="Nzoom\Export\Adapter\JsonExportFormatAdapter::processExportRecord">processExportRecord</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Entity/ExportData.php.html#370"><abbr title="Nzoom\Export\Entity\ExportData::toArray">toArray</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Entity/ExportData.php.html#352"><abbr title="Nzoom\Export\Entity\ExportData::validate">validate</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#1088"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::optimizeMemoryForExport">optimizeMemoryForExport</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Entity/ExportData.php.html#201"><abbr title="Nzoom\Export\Entity\ExportData::getRecords">getRecords</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Entity/ExportValue.php.html#113"><abbr title="Nzoom\Export\Entity\ExportValue::setType">setType</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Factory/ExportFormatFactory.php.html#227"><abbr title="Nzoom\Export\Factory\ExportFormatFactory::createAdapterFromFilename">createAdapterFromFilename</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Streamer/StreamHeaders.php.html#117"><abbr title="Nzoom\Export\Streamer\StreamHeaders::sanitizeFilename">sanitizeFilename</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Streamer/PointerFileStreamer.php.html#121"><abbr title="Nzoom\Export\Streamer\PointerFileStreamer::setChunkSize">setChunkSize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Factory/ExportFormatFactory.php.html#171"><abbr title="Nzoom\Export\Factory\ExportFormatFactory::isValidAdapter">isValidAdapter</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Streamer/GeneratorFileStreamer.php.html#56"><abbr title="Nzoom\Export\Streamer\GeneratorFileStreamer::initializeGenerator">initializeGenerator</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Streamer/GeneratorFileStreamer.php.html#123"><abbr title="Nzoom\Export\Streamer\GeneratorFileStreamer::setTotalSize">setTotalSize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#253"><abbr title="Nzoom\Export\Streamer\FileStreamer::increaseExecutionTime">increaseExecutionTime</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Streamer/GeneratorFileStreamer.php.html#36"><abbr title="Nzoom\Export\Streamer\GeneratorFileStreamer::__construct">__construct</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#265"><abbr title="Nzoom\Export\Streamer\FileStreamer::cleanup">cleanup</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Provider/ModelTableProvider.php.html#419"><abbr title="Nzoom\Export\Provider\ModelTableProvider::validateRecord">validateRecord</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#225"><abbr title="Nzoom\Export\Streamer\FileStreamer::outputChunk">outputChunk</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#200"><abbr title="Nzoom\Export\Streamer\FileStreamer::setStreamingOptimizationHeaders">setStreamingOptimizationHeaders</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Streamer/PointerFileStreamer.php.html#93"><abbr title="Nzoom\Export\Streamer\PointerFileStreamer::cleanup">cleanup</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#175"><abbr title="Nzoom\Export\Streamer\FileStreamer::prepareForStreaming">prepareForStreaming</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#142"><abbr title="Nzoom\Export\Streamer\FileStreamer::stream">stream</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#71"><abbr title="Nzoom\Export\Streamer\FileStreamer::getHeaders">getHeaders</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Streamer/StreamHeaders.php.html#189"><abbr title="Nzoom\Export\Streamer\StreamHeaders::flushHeaders">flushHeaders</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Adapter/AbstractExportFormatAdapter.php.html#43"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::__construct">__construct</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ExportService.php.html#259"><abbr title="Nzoom\Export\ExportService::getAdapter">getAdapter</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ExportService.php.html#232"><abbr title="Nzoom\Export\ExportService::getFormatFactory">getFormatFactory</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Adapter/AbstractExportFormatAdapter.php.html#213"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::validateStringTarget">validateStringTarget</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Adapter/CsvExportFormatAdapter.php.html#209"><abbr title="Nzoom\Export\Adapter\CsvExportFormatAdapter::processExportRecord">processExportRecord</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Adapter/CsvExportFormatAdapter.php.html#296"><abbr title="Nzoom\Export\Adapter\CsvExportFormatAdapter::getDecimalPlaces">getDecimalPlaces</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#244"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::setDocumentProperties">setDocumentProperties</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#295"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::addHeaders">addHeaders</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#630"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::handleExportRecordCellError">handleExportRecordCellError</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#648"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::finalizeExportDataColumns">finalizeExportDataColumns</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#915"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::addTableDataToWorksheet">addTableDataToWorksheet</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Adapter/JsonExportFormatAdapter.php.html#176"><abbr title="Nzoom\Export\Adapter\JsonExportFormatAdapter::prepareObjectStructure">prepareObjectStructure</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="DataFactory.php.html#84"><abbr title="Nzoom\Export\DataFactory::__invoke">__invoke</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="DataFactory.php.html#147"><abbr title="Nzoom\Export\DataFactory::processModelsChunk">processModelsChunk</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="DataFactory.php.html#269"><abbr title="Nzoom\Export\DataFactory::createStreaming">createStreaming</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Entity/ExportData.php.html#244"><abbr title="Nzoom\Export\Entity\ExportData::count">count</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Entity/ExportData.php.html#261"><abbr title="Nzoom\Export\Entity\ExportData::createRecord">createRecord</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Entity/ExportData.php.html#390"><abbr title="Nzoom\Export\Entity\ExportData::getIterator">getIterator</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Entity/ExportData.php.html#277"><abbr title="Nzoom\Export\Entity\ExportData::isEmpty">isEmpty</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Entity/ExportTableCollection.php.html#81"><abbr title="Nzoom\Export\Entity\ExportTableCollection::removeTable">removeTable</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ExportService.php.html#181"><abbr title="Nzoom\Export\ExportService::createTempStream">createTempStream</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ExportActionFactory.php.html#397"><abbr title="Nzoom\Export\ExportActionFactory::getDelimiterOptions">getDelimiterOptions</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ExportActionFactory.php.html#329"><abbr title="Nzoom\Export\ExportActionFactory::getFormatOptions">getFormatOptions</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ExportActionFactory.php.html#194"><abbr title="Nzoom\Export\ExportActionFactory::firstOrZero">firstOrZero</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Entity/ExportTableCollection.php.html#252"><abbr title="Nzoom\Export\Entity\ExportTableCollection::toArray">toArray</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Entity/ExportTableCollection.php.html#216"><abbr title="Nzoom\Export\Entity\ExportTableCollection::getTotalRecordCount">getTotalRecordCount</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Entity/ExportTableCollection.php.html#39"><abbr title="Nzoom\Export\Entity\ExportTableCollection::addTable">addTable</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Entity/ExportHeader.php.html#111"><abbr title="Nzoom\Export\Entity\ExportHeader::getColumnByVarName">getColumnByVarName</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Entity/ExportRecord.php.html#276"><abbr title="Nzoom\Export\Entity\ExportRecord::addTable">addTable</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Entity/ExportRecord.php.html#266"><abbr title="Nzoom\Export\Entity\ExportRecord::getTable">getTable</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Entity/ExportRecord.php.html#255"><abbr title="Nzoom\Export\Entity\ExportRecord::hasTables">hasTables</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Entity/ExportRecord.php.html#143"><abbr title="Nzoom\Export\Entity\ExportRecord::getValueByColumnName">getValueByColumnName</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Entity/ExportRecord.php.html#103"><abbr title="Nzoom\Export\Entity\ExportRecord::setValueByColumnName">setValueByColumnName</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Entity/ExportColumn.php.html#131"><abbr title="Nzoom\Export\Entity\ExportColumn::setType">setType</abbr></a></td><td class="text-right">3</td></tr>
       <tr><td><a href="Entity/ExportHeader.php.html#57"><abbr title="Nzoom\Export\Entity\ExportHeader::addColumn">addColumn</abbr></a></td><td class="text-right">2</td></tr>
       <tr><td><a href="Entity/ExportColumn.php.html#86"><abbr title="Nzoom\Export\Entity\ExportColumn::setVarName">setVarName</abbr></a></td><td class="text-right">2</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 9.2.32</a> using <a href="https://secure.php.net/" target="_top">PHP 7.4.33</a> and <a href="https://phpunit.de/">PHPUnit 9.6.23</a> at Fri Jun 20 9:59:42 UTC 2025.</small>
    </p>
   </footer>
  </div>
  <script src="_js/jquery.min.js?v=9.2.32" type="text/javascript"></script>
  <script src="_js/d3.min.js?v=9.2.32" type="text/javascript"></script>
  <script src="_js/nv.d3.min.js?v=9.2.32" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([15,0,0,3,0,0,0,1,0,1,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([296,0,0,2,1,0,1,1,1,2,0,38], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,38,"<a href=\"Adapter\/AbstractExportFormatAdapter.php.html#12\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter<\/a>"],[0,79,"<a href=\"Adapter\/CsvExportFormatAdapter.php.html#15\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter<\/a>"],[0,233,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#27\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter<\/a>"],[0,71,"<a href=\"Adapter\/JsonExportFormatAdapter.php.html#15\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter<\/a>"],[0,33,"<a href=\"DataFactory.php.html#17\">Nzoom\\Export\\DataFactory<\/a>"],[60,18,"<a href=\"Entity\/ExportColumn.php.html#10\">Nzoom\\Export\\Entity\\ExportColumn<\/a>"],[0,56,"<a href=\"Entity\/ExportData.php.html#12\">Nzoom\\Export\\Entity\\ExportData<\/a>"],[28.78787878787879,33,"<a href=\"Entity\/ExportHeader.php.html#11\">Nzoom\\Export\\Entity\\ExportHeader<\/a>"],[22.22222222222222,35,"<a href=\"Entity\/ExportRecord.php.html#11\">Nzoom\\Export\\Entity\\ExportRecord<\/a>"],[86.8421052631579,30,"<a href=\"Entity\/ExportTable.php.html#11\">Nzoom\\Export\\Entity\\ExportTable<\/a>"],[0,32,"<a href=\"Entity\/ExportTableCollection.php.html#11\">Nzoom\\Export\\Entity\\ExportTableCollection<\/a>"],[21.428571428571427,61,"<a href=\"Entity\/ExportValue.php.html#10\">Nzoom\\Export\\Entity\\ExportValue<\/a>"],[0,26,"<a href=\"ExportActionFactory.php.html#11\">Nzoom\\Export\\ExportActionFactory<\/a>"],[0,29,"<a href=\"ExportService.php.html#13\">Nzoom\\Export\\ExportService<\/a>"],[0,25,"<a href=\"Factory\/ExportFormatFactory.php.html#13\">Nzoom\\Export\\Factory\\ExportFormatFactory<\/a>"],[0,58,"<a href=\"Provider\/ModelTableProvider.php.html#18\">Nzoom\\Export\\Provider\\ModelTableProvider<\/a>"],[0,23,"<a href=\"Streamer\/FileStreamer.php.html#12\">Nzoom\\Export\\Streamer\\FileStreamer<\/a>"],[0,20,"<a href=\"Streamer\/GeneratorFileStreamer.php.html#11\">Nzoom\\Export\\Streamer\\GeneratorFileStreamer<\/a>"],[0,18,"<a href=\"Streamer\/PointerFileStreamer.php.html#11\">Nzoom\\Export\\Streamer\\PointerFileStreamer<\/a>"],[0,27,"<a href=\"Streamer\/StreamHeaders.php.html#11\">Nzoom\\Export\\Streamer\\StreamHeaders<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,2,"<a href=\"Adapter\/AbstractExportFormatAdapter.php.html#43\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::__construct<\/a>"],[0,1,"<a href=\"Adapter\/AbstractExportFormatAdapter.php.html#57\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::setConfiguration<\/a>"],[0,5,"<a href=\"Adapter\/AbstractExportFormatAdapter.php.html#70\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::getExportFilename<\/a>"],[0,3,"<a href=\"Adapter\/AbstractExportFormatAdapter.php.html#100\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::sendHeaders<\/a>"],[0,4,"<a href=\"Adapter\/AbstractExportFormatAdapter.php.html#127\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::handleExportError<\/a>"],[0,5,"<a href=\"Adapter\/AbstractExportFormatAdapter.php.html#161\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::getRecordHeaders<\/a>"],[0,1,"<a href=\"Adapter\/AbstractExportFormatAdapter.php.html#181\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::getFormatOptions<\/a>"],[0,3,"<a href=\"Adapter\/AbstractExportFormatAdapter.php.html#193\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::validateAndPrepareSaveTarget<\/a>"],[0,2,"<a href=\"Adapter\/AbstractExportFormatAdapter.php.html#213\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::validateStringTarget<\/a>"],[0,5,"<a href=\"Adapter\/AbstractExportFormatAdapter.php.html#231\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::validatePhpStreamWrapper<\/a>"],[0,3,"<a href=\"Adapter\/AbstractExportFormatAdapter.php.html#273\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::validateFilePath<\/a>"],[0,4,"<a href=\"Adapter\/AbstractExportFormatAdapter.php.html#295\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::validateFilePointer<\/a>"],[0,4,"<a href=\"Adapter\/CsvExportFormatAdapter.php.html#50\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::export<\/a>"],[0,19,"<a href=\"Adapter\/CsvExportFormatAdapter.php.html#85\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::extractCsvOptions<\/a>"],[0,6,"<a href=\"Adapter\/CsvExportFormatAdapter.php.html#128\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::writeCsvContent<\/a>"],[0,7,"<a href=\"Adapter\/CsvExportFormatAdapter.php.html#173\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::processExportDataRecords<\/a>"],[0,2,"<a href=\"Adapter\/CsvExportFormatAdapter.php.html#209\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::processExportRecord<\/a>"],[0,12,"<a href=\"Adapter\/CsvExportFormatAdapter.php.html#227\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::formatValueForCsv<\/a>"],[0,7,"<a href=\"Adapter\/CsvExportFormatAdapter.php.html#271\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::formatDateValue<\/a>"],[0,2,"<a href=\"Adapter\/CsvExportFormatAdapter.php.html#296\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::getDecimalPlaces<\/a>"],[0,4,"<a href=\"Adapter\/CsvExportFormatAdapter.php.html#312\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::getDelimiter<\/a>"],[0,6,"<a href=\"Adapter\/CsvExportFormatAdapter.php.html#339\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::normalizeDelimiter<\/a>"],[0,1,"<a href=\"Adapter\/CsvExportFormatAdapter.php.html#360\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::setDateFormat<\/a>"],[0,1,"<a href=\"Adapter\/CsvExportFormatAdapter.php.html#371\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::setDatetimeFormat<\/a>"],[0,1,"<a href=\"Adapter\/CsvExportFormatAdapter.php.html#381\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::getDateFormat<\/a>"],[0,1,"<a href=\"Adapter\/CsvExportFormatAdapter.php.html#391\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::getDatetimeFormat<\/a>"],[0,1,"<a href=\"Adapter\/CsvExportFormatAdapter.php.html#399\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::getSupportedExtensions<\/a>"],[0,1,"<a href=\"Adapter\/CsvExportFormatAdapter.php.html#407\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::getMimeType<\/a>"],[0,1,"<a href=\"Adapter\/CsvExportFormatAdapter.php.html#416\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::getDefaultExtension<\/a>"],[0,1,"<a href=\"Adapter\/CsvExportFormatAdapter.php.html#424\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::supportsFormat<\/a>"],[0,1,"<a href=\"Adapter\/CsvExportFormatAdapter.php.html#432\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::getFormatName<\/a>"],[0,1,"<a href=\"Adapter\/CsvExportFormatAdapter.php.html#440\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::getFormatOptions<\/a>"],[0,5,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#56\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::export<\/a>"],[0,11,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#106\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::extractSizingOptions<\/a>"],[0,1,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#138\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::createSpreadsheet<\/a>"],[0,4,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#170\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::setSpreadsheetLocale<\/a>"],[0,4,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#193\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::getApplicationLocale<\/a>"],[0,2,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#244\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::setDocumentProperties<\/a>"],[0,1,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#265\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::processExportData<\/a>"],[0,2,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#295\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::addHeaders<\/a>"],[0,4,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#310\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::addNamedRanges<\/a>"],[0,1,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#346\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::styleHeaderRow<\/a>"],[0,8,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#362\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::processExportDataRecords<\/a>"],[0,3,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#411\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::processExportRecord<\/a>"],[0,17,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#436\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::setCellValueWithFormatting<\/a>"],[0,5,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#497\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::setCellDateValue<\/a>"],[0,15,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#525\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::getExcelFormatFromExportValue<\/a>"],[0,3,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#576\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::convertCustomNumberFormat<\/a>"],[0,1,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#599\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::convertCustomDateFormat<\/a>"],[0,2,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#630\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::handleExportRecordCellError<\/a>"],[0,2,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#648\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::finalizeExportDataColumns<\/a>"],[0,5,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#672\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::applyColumnWidthConstraints<\/a>"],[0,8,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#709\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::applyRowHeightConstraints<\/a>"],[0,3,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#766\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::applyVerticalAlignment<\/a>"],[0,4,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#785\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::processExportTables<\/a>"],[0,5,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#809\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::collectTablesByType<\/a>"],[0,4,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#839\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::createTableWorksheet<\/a>"],[0,1,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#867\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::sanitizeWorksheetName<\/a>"],[0,3,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#881\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::populateTableWorksheet<\/a>"],[0,2,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#915\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::addTableDataToWorksheet<\/a>"],[0,4,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#935\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::createWriter<\/a>"],[0,4,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#956\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::handleSpreadsheetError<\/a>"],[0,83,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#986\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::getExcelFormatting<\/a>"],[0,3,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#1088\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::optimizeMemoryForExport<\/a>"],[0,4,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#1111\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::convertToBytes<\/a>"],[0,1,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#1134\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::getSupportedExtensions<\/a>"],[0,4,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#1142\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::getMimeType<\/a>"],[0,1,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#1159\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::getDefaultExtension<\/a>"],[0,1,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#1167\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::supportsFormat<\/a>"],[0,1,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#1175\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::getFormatName<\/a>"],[0,1,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#1183\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::getFormatOptions<\/a>"],[0,4,"<a href=\"Adapter\/JsonExportFormatAdapter.php.html#30\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::export<\/a>"],[0,7,"<a href=\"Adapter\/JsonExportFormatAdapter.php.html#65\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::extractJsonOptions<\/a>"],[0,6,"<a href=\"Adapter\/JsonExportFormatAdapter.php.html#89\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::writeJsonContent<\/a>"],[0,5,"<a href=\"Adapter\/JsonExportFormatAdapter.php.html#129\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::prepareJsonData<\/a>"],[0,5,"<a href=\"Adapter\/JsonExportFormatAdapter.php.html#148\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::prepareArrayStructure<\/a>"],[0,2,"<a href=\"Adapter\/JsonExportFormatAdapter.php.html#176\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::prepareObjectStructure<\/a>"],[0,6,"<a href=\"Adapter\/JsonExportFormatAdapter.php.html#201\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::prepareNestedStructure<\/a>"],[0,3,"<a href=\"Adapter\/JsonExportFormatAdapter.php.html#233\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::processExportRecord<\/a>"],[0,9,"<a href=\"Adapter\/JsonExportFormatAdapter.php.html#259\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::formatValueForJson<\/a>"],[0,7,"<a href=\"Adapter\/JsonExportFormatAdapter.php.html#297\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::formatDateValue<\/a>"],[0,1,"<a href=\"Adapter\/JsonExportFormatAdapter.php.html#324\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::addMetadata<\/a>"],[0,3,"<a href=\"Adapter\/JsonExportFormatAdapter.php.html#338\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::getMetadata<\/a>"],[0,7,"<a href=\"Adapter\/JsonExportFormatAdapter.php.html#373\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::getJsonOptions<\/a>"],[0,1,"<a href=\"Adapter\/JsonExportFormatAdapter.php.html#428\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::getSupportedExtensions<\/a>"],[0,1,"<a href=\"Adapter\/JsonExportFormatAdapter.php.html#436\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::getMimeType<\/a>"],[0,1,"<a href=\"Adapter\/JsonExportFormatAdapter.php.html#445\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::getDefaultExtension<\/a>"],[0,1,"<a href=\"Adapter\/JsonExportFormatAdapter.php.html#453\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::supportsFormat<\/a>"],[0,1,"<a href=\"Adapter\/JsonExportFormatAdapter.php.html#461\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::getFormatName<\/a>"],[0,1,"<a href=\"Adapter\/JsonExportFormatAdapter.php.html#469\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::getFormatOptions<\/a>"],[0,1,"<a href=\"DataFactory.php.html#39\">Nzoom\\Export\\DataFactory::__construct<\/a>"],[0,1,"<a href=\"DataFactory.php.html#49\">Nzoom\\Export\\DataFactory::setTableProvider<\/a>"],[0,1,"<a href=\"DataFactory.php.html#59\">Nzoom\\Export\\DataFactory::isTablesEnabled<\/a>"],[0,1,"<a href=\"DataFactory.php.html#70\">Nzoom\\Export\\DataFactory::withModelTableProvider<\/a>"],[0,2,"<a href=\"DataFactory.php.html#84\">Nzoom\\Export\\DataFactory::__invoke<\/a>"],[0,6,"<a href=\"DataFactory.php.html#112\">Nzoom\\Export\\DataFactory::createHeaderFromOutlook<\/a>"],[0,2,"<a href=\"DataFactory.php.html#147\">Nzoom\\Export\\DataFactory::processModelsChunk<\/a>"],[0,6,"<a href=\"DataFactory.php.html#161\">Nzoom\\Export\\DataFactory::createRecordFromModel<\/a>"],[0,5,"<a href=\"DataFactory.php.html#202\">Nzoom\\Export\\DataFactory::extractTablesForRecord<\/a>"],[0,1,"<a href=\"DataFactory.php.html#226\">Nzoom\\Export\\DataFactory::mapFieldTypeToExportType<\/a>"],[0,1,"<a href=\"DataFactory.php.html#255\">Nzoom\\Export\\DataFactory::setChunkSize<\/a>"],[0,2,"<a href=\"DataFactory.php.html#269\">Nzoom\\Export\\DataFactory::createStreaming<\/a>"],[0,4,"<a href=\"DataFactory.php.html#318\">Nzoom\\Export\\DataFactory::createCursorStreaming<\/a>"],[100,1,"<a href=\"Entity\/ExportColumn.php.html#53\">Nzoom\\Export\\Entity\\ExportColumn::__construct<\/a>"],[100,1,"<a href=\"Entity\/ExportColumn.php.html#74\">Nzoom\\Export\\Entity\\ExportColumn::getVarName<\/a>"],[66.66666666666666,2,"<a href=\"Entity\/ExportColumn.php.html#86\">Nzoom\\Export\\Entity\\ExportColumn::setVarName<\/a>"],[100,1,"<a href=\"Entity\/ExportColumn.php.html#100\">Nzoom\\Export\\Entity\\ExportColumn::getLabel<\/a>"],[100,1,"<a href=\"Entity\/ExportColumn.php.html#110\">Nzoom\\Export\\Entity\\ExportColumn::setLabel<\/a>"],[0,1,"<a href=\"Entity\/ExportColumn.php.html#120\">Nzoom\\Export\\Entity\\ExportColumn::getType<\/a>"],[28.57142857142857,2,"<a href=\"Entity\/ExportColumn.php.html#131\">Nzoom\\Export\\Entity\\ExportColumn::setType<\/a>"],[0,1,"<a href=\"Entity\/ExportColumn.php.html#150\">Nzoom\\Export\\Entity\\ExportColumn::getFormat<\/a>"],[100,1,"<a href=\"Entity\/ExportColumn.php.html#161\">Nzoom\\Export\\Entity\\ExportColumn::setFormat<\/a>"],[0,1,"<a href=\"Entity\/ExportColumn.php.html#171\">Nzoom\\Export\\Entity\\ExportColumn::getWidth<\/a>"],[100,1,"<a href=\"Entity\/ExportColumn.php.html#181\">Nzoom\\Export\\Entity\\ExportColumn::setWidth<\/a>"],[0,1,"<a href=\"Entity\/ExportColumn.php.html#191\">Nzoom\\Export\\Entity\\ExportColumn::getStyles<\/a>"],[100,1,"<a href=\"Entity\/ExportColumn.php.html#201\">Nzoom\\Export\\Entity\\ExportColumn::setStyles<\/a>"],[0,1,"<a href=\"Entity\/ExportColumn.php.html#212\">Nzoom\\Export\\Entity\\ExportColumn::addStyle<\/a>"],[100,1,"<a href=\"Entity\/ExportColumn.php.html#223\">Nzoom\\Export\\Entity\\ExportColumn::validateValue<\/a>"],[0,1,"<a href=\"Entity\/ExportColumn.php.html#235\">Nzoom\\Export\\Entity\\ExportColumn::createValue<\/a>"],[0,1,"<a href=\"Entity\/ExportData.php.html#52\">Nzoom\\Export\\Entity\\ExportData::__construct<\/a>"],[0,1,"<a href=\"Entity\/ExportData.php.html#66\">Nzoom\\Export\\Entity\\ExportData::setRecordProvider<\/a>"],[0,1,"<a href=\"Entity\/ExportData.php.html#83\">Nzoom\\Export\\Entity\\ExportData::isLazy<\/a>"],[0,1,"<a href=\"Entity\/ExportData.php.html#93\">Nzoom\\Export\\Entity\\ExportData::getPageSize<\/a>"],[0,1,"<a href=\"Entity\/ExportData.php.html#104\">Nzoom\\Export\\Entity\\ExportData::setPageSize<\/a>"],[0,1,"<a href=\"Entity\/ExportData.php.html#115\">Nzoom\\Export\\Entity\\ExportData::getHeader<\/a>"],[0,1,"<a href=\"Entity\/ExportData.php.html#125\">Nzoom\\Export\\Entity\\ExportData::setHeader<\/a>"],[0,1,"<a href=\"Entity\/ExportData.php.html#135\">Nzoom\\Export\\Entity\\ExportData::getMetadata<\/a>"],[0,1,"<a href=\"Entity\/ExportData.php.html#145\">Nzoom\\Export\\Entity\\ExportData::setMetadata<\/a>"],[0,1,"<a href=\"Entity\/ExportData.php.html#157\">Nzoom\\Export\\Entity\\ExportData::getMetadataValue<\/a>"],[0,1,"<a href=\"Entity\/ExportData.php.html#168\">Nzoom\\Export\\Entity\\ExportData::setMetadataValue<\/a>"],[0,4,"<a href=\"Entity\/ExportData.php.html#182\">Nzoom\\Export\\Entity\\ExportData::addRecord<\/a>"],[0,3,"<a href=\"Entity\/ExportData.php.html#201\">Nzoom\\Export\\Entity\\ExportData::getRecords<\/a>"],[0,4,"<a href=\"Entity\/ExportData.php.html#222\">Nzoom\\Export\\Entity\\ExportData::getRecordAt<\/a>"],[0,2,"<a href=\"Entity\/ExportData.php.html#244\">Nzoom\\Export\\Entity\\ExportData::count<\/a>"],[0,2,"<a href=\"Entity\/ExportData.php.html#261\">Nzoom\\Export\\Entity\\ExportData::createRecord<\/a>"],[0,2,"<a href=\"Entity\/ExportData.php.html#277\">Nzoom\\Export\\Entity\\ExportData::isEmpty<\/a>"],[0,15,"<a href=\"Entity\/ExportData.php.html#293\">Nzoom\\Export\\Entity\\ExportData::sortByColumn<\/a>"],[0,1,"<a href=\"Entity\/ExportData.php.html#341\">Nzoom\\Export\\Entity\\ExportData::filter<\/a>"],[0,3,"<a href=\"Entity\/ExportData.php.html#352\">Nzoom\\Export\\Entity\\ExportData::validate<\/a>"],[0,3,"<a href=\"Entity\/ExportData.php.html#370\">Nzoom\\Export\\Entity\\ExportData::toArray<\/a>"],[0,2,"<a href=\"Entity\/ExportData.php.html#390\">Nzoom\\Export\\Entity\\ExportData::getIterator<\/a>"],[0,4,"<a href=\"Entity\/ExportData.php.html#406\">Nzoom\\Export\\Entity\\ExportData::getLazyIterator<\/a>"],[100,1,"<a href=\"Entity\/ExportHeader.php.html#44\">Nzoom\\Export\\Entity\\ExportHeader::__construct<\/a>"],[50,2,"<a href=\"Entity\/ExportHeader.php.html#57\">Nzoom\\Export\\Entity\\ExportHeader::addColumn<\/a>"],[100,1,"<a href=\"Entity\/ExportHeader.php.html#79\">Nzoom\\Export\\Entity\\ExportHeader::hasColumn<\/a>"],[0,1,"<a href=\"Entity\/ExportHeader.php.html#89\">Nzoom\\Export\\Entity\\ExportHeader::getColumns<\/a>"],[0,1,"<a href=\"Entity\/ExportHeader.php.html#100\">Nzoom\\Export\\Entity\\ExportHeader::getColumnAt<\/a>"],[0,2,"<a href=\"Entity\/ExportHeader.php.html#111\">Nzoom\\Export\\Entity\\ExportHeader::getColumnByVarName<\/a>"],[0,1,"<a href=\"Entity\/ExportHeader.php.html#125\">Nzoom\\Export\\Entity\\ExportHeader::getBackgroundColor<\/a>"],[0,1,"<a href=\"Entity\/ExportHeader.php.html#135\">Nzoom\\Export\\Entity\\ExportHeader::setBackgroundColor<\/a>"],[0,1,"<a href=\"Entity\/ExportHeader.php.html#145\">Nzoom\\Export\\Entity\\ExportHeader::getStyles<\/a>"],[0,1,"<a href=\"Entity\/ExportHeader.php.html#155\">Nzoom\\Export\\Entity\\ExportHeader::setStyles<\/a>"],[0,1,"<a href=\"Entity\/ExportHeader.php.html#166\">Nzoom\\Export\\Entity\\ExportHeader::addStyle<\/a>"],[100,1,"<a href=\"Entity\/ExportHeader.php.html#176\">Nzoom\\Export\\Entity\\ExportHeader::count<\/a>"],[100,1,"<a href=\"Entity\/ExportHeader.php.html#186\">Nzoom\\Export\\Entity\\ExportHeader::getLabels<\/a>"],[0,1,"<a href=\"Entity\/ExportHeader.php.html#198\">Nzoom\\Export\\Entity\\ExportHeader::getVarNames<\/a>"],[0,1,"<a href=\"Entity\/ExportHeader.php.html#210\">Nzoom\\Export\\Entity\\ExportHeader::getTypes<\/a>"],[0,6,"<a href=\"Entity\/ExportHeader.php.html#223\">Nzoom\\Export\\Entity\\ExportHeader::reorderColumns<\/a>"],[77.77777777777779,5,"<a href=\"Entity\/ExportHeader.php.html#264\">Nzoom\\Export\\Entity\\ExportHeader::validateRecord<\/a>"],[0,1,"<a href=\"Entity\/ExportHeader.php.html#291\">Nzoom\\Export\\Entity\\ExportHeader::rewind<\/a>"],[0,1,"<a href=\"Entity\/ExportHeader.php.html#301\">Nzoom\\Export\\Entity\\ExportHeader::current<\/a>"],[0,1,"<a href=\"Entity\/ExportHeader.php.html#311\">Nzoom\\Export\\Entity\\ExportHeader::key<\/a>"],[0,1,"<a href=\"Entity\/ExportHeader.php.html#319\">Nzoom\\Export\\Entity\\ExportHeader::next<\/a>"],[0,1,"<a href=\"Entity\/ExportHeader.php.html#329\">Nzoom\\Export\\Entity\\ExportHeader::valid<\/a>"],[100,1,"<a href=\"Entity\/ExportRecord.php.html#43\">Nzoom\\Export\\Entity\\ExportRecord::__construct<\/a>"],[80,2,"<a href=\"Entity\/ExportRecord.php.html#57\">Nzoom\\Export\\Entity\\ExportRecord::addValue<\/a>"],[0,4,"<a href=\"Entity\/ExportRecord.php.html#76\">Nzoom\\Export\\Entity\\ExportRecord::setValueAt<\/a>"],[0,2,"<a href=\"Entity\/ExportRecord.php.html#103\">Nzoom\\Export\\Entity\\ExportRecord::setValueByColumnName<\/a>"],[0,1,"<a href=\"Entity\/ExportRecord.php.html#121\">Nzoom\\Export\\Entity\\ExportRecord::getValues<\/a>"],[100,1,"<a href=\"Entity\/ExportRecord.php.html#132\">Nzoom\\Export\\Entity\\ExportRecord::getValueAt<\/a>"],[0,2,"<a href=\"Entity\/ExportRecord.php.html#143\">Nzoom\\Export\\Entity\\ExportRecord::getValueByColumnName<\/a>"],[0,1,"<a href=\"Entity\/ExportRecord.php.html#158\">Nzoom\\Export\\Entity\\ExportRecord::hasValue<\/a>"],[100,1,"<a href=\"Entity\/ExportRecord.php.html#168\">Nzoom\\Export\\Entity\\ExportRecord::getRawValues<\/a>"],[0,1,"<a href=\"Entity\/ExportRecord.php.html#180\">Nzoom\\Export\\Entity\\ExportRecord::getFormattedValues<\/a>"],[0,1,"<a href=\"Entity\/ExportRecord.php.html#192\">Nzoom\\Export\\Entity\\ExportRecord::getMetadata<\/a>"],[0,1,"<a href=\"Entity\/ExportRecord.php.html#202\">Nzoom\\Export\\Entity\\ExportRecord::setMetadata<\/a>"],[0,1,"<a href=\"Entity\/ExportRecord.php.html#214\">Nzoom\\Export\\Entity\\ExportRecord::getMetadataValue<\/a>"],[0,1,"<a href=\"Entity\/ExportRecord.php.html#225\">Nzoom\\Export\\Entity\\ExportRecord::setMetadataValue<\/a>"],[0,1,"<a href=\"Entity\/ExportRecord.php.html#235\">Nzoom\\Export\\Entity\\ExportRecord::getTableCollection<\/a>"],[0,1,"<a href=\"Entity\/ExportRecord.php.html#245\">Nzoom\\Export\\Entity\\ExportRecord::setTableCollection<\/a>"],[0,2,"<a href=\"Entity\/ExportRecord.php.html#255\">Nzoom\\Export\\Entity\\ExportRecord::hasTables<\/a>"],[0,2,"<a href=\"Entity\/ExportRecord.php.html#266\">Nzoom\\Export\\Entity\\ExportRecord::getTable<\/a>"],[0,2,"<a href=\"Entity\/ExportRecord.php.html#276\">Nzoom\\Export\\Entity\\ExportRecord::addTable<\/a>"],[100,1,"<a href=\"Entity\/ExportRecord.php.html#291\">Nzoom\\Export\\Entity\\ExportRecord::validate<\/a>"],[100,1,"<a href=\"Entity\/ExportRecord.php.html#301\">Nzoom\\Export\\Entity\\ExportRecord::count<\/a>"],[0,1,"<a href=\"Entity\/ExportRecord.php.html#311\">Nzoom\\Export\\Entity\\ExportRecord::rewind<\/a>"],[0,1,"<a href=\"Entity\/ExportRecord.php.html#321\">Nzoom\\Export\\Entity\\ExportRecord::current<\/a>"],[0,1,"<a href=\"Entity\/ExportRecord.php.html#331\">Nzoom\\Export\\Entity\\ExportRecord::key<\/a>"],[0,1,"<a href=\"Entity\/ExportRecord.php.html#339\">Nzoom\\Export\\Entity\\ExportRecord::next<\/a>"],[0,1,"<a href=\"Entity\/ExportRecord.php.html#349\">Nzoom\\Export\\Entity\\ExportRecord::valid<\/a>"],[100,1,"<a href=\"Entity\/ExportTable.php.html#52\">Nzoom\\Export\\Entity\\ExportTable::__construct<\/a>"],[100,1,"<a href=\"Entity\/ExportTable.php.html#71\">Nzoom\\Export\\Entity\\ExportTable::getTableType<\/a>"],[100,1,"<a href=\"Entity\/ExportTable.php.html#81\">Nzoom\\Export\\Entity\\ExportTable::getTableName<\/a>"],[0,1,"<a href=\"Entity\/ExportTable.php.html#91\">Nzoom\\Export\\Entity\\ExportTable::setTableName<\/a>"],[100,1,"<a href=\"Entity\/ExportTable.php.html#101\">Nzoom\\Export\\Entity\\ExportTable::getHeader<\/a>"],[0,1,"<a href=\"Entity\/ExportTable.php.html#111\">Nzoom\\Export\\Entity\\ExportTable::setHeader<\/a>"],[100,1,"<a href=\"Entity\/ExportTable.php.html#121\">Nzoom\\Export\\Entity\\ExportTable::getParentReference<\/a>"],[0,1,"<a href=\"Entity\/ExportTable.php.html#131\">Nzoom\\Export\\Entity\\ExportTable::setParentReference<\/a>"],[100,3,"<a href=\"Entity\/ExportTable.php.html#143\">Nzoom\\Export\\Entity\\ExportTable::addRecord<\/a>"],[100,1,"<a href=\"Entity\/ExportTable.php.html#157\">Nzoom\\Export\\Entity\\ExportTable::getRecords<\/a>"],[80,4,"<a href=\"Entity\/ExportTable.php.html#169\">Nzoom\\Export\\Entity\\ExportTable::setRecords<\/a>"],[100,1,"<a href=\"Entity\/ExportTable.php.html#185\">Nzoom\\Export\\Entity\\ExportTable::clearRecords<\/a>"],[100,1,"<a href=\"Entity\/ExportTable.php.html#195\">Nzoom\\Export\\Entity\\ExportTable::hasRecords<\/a>"],[100,1,"<a href=\"Entity\/ExportTable.php.html#205\">Nzoom\\Export\\Entity\\ExportTable::getMetadata<\/a>"],[0,1,"<a href=\"Entity\/ExportTable.php.html#215\">Nzoom\\Export\\Entity\\ExportTable::setMetadata<\/a>"],[100,1,"<a href=\"Entity\/ExportTable.php.html#227\">Nzoom\\Export\\Entity\\ExportTable::getMetadataValue<\/a>"],[100,1,"<a href=\"Entity\/ExportTable.php.html#238\">Nzoom\\Export\\Entity\\ExportTable::setMetadataValue<\/a>"],[100,3,"<a href=\"Entity\/ExportTable.php.html#248\">Nzoom\\Export\\Entity\\ExportTable::validate<\/a>"],[100,3,"<a href=\"Entity\/ExportTable.php.html#266\">Nzoom\\Export\\Entity\\ExportTable::toArray<\/a>"],[100,1,"<a href=\"Entity\/ExportTable.php.html#286\">Nzoom\\Export\\Entity\\ExportTable::count<\/a>"],[100,1,"<a href=\"Entity\/ExportTable.php.html#296\">Nzoom\\Export\\Entity\\ExportTable::getIterator<\/a>"],[0,1,"<a href=\"Entity\/ExportTableCollection.php.html#28\">Nzoom\\Export\\Entity\\ExportTableCollection::__construct<\/a>"],[0,2,"<a href=\"Entity\/ExportTableCollection.php.html#39\">Nzoom\\Export\\Entity\\ExportTableCollection::addTable<\/a>"],[0,1,"<a href=\"Entity\/ExportTableCollection.php.html#59\">Nzoom\\Export\\Entity\\ExportTableCollection::getTable<\/a>"],[0,1,"<a href=\"Entity\/ExportTableCollection.php.html#70\">Nzoom\\Export\\Entity\\ExportTableCollection::hasTable<\/a>"],[0,2,"<a href=\"Entity\/ExportTableCollection.php.html#81\">Nzoom\\Export\\Entity\\ExportTableCollection::removeTable<\/a>"],[0,1,"<a href=\"Entity\/ExportTableCollection.php.html#96\">Nzoom\\Export\\Entity\\ExportTableCollection::getTables<\/a>"],[0,1,"<a href=\"Entity\/ExportTableCollection.php.html#106\">Nzoom\\Export\\Entity\\ExportTableCollection::getTableTypes<\/a>"],[0,1,"<a href=\"Entity\/ExportTableCollection.php.html#114\">Nzoom\\Export\\Entity\\ExportTableCollection::clearTables<\/a>"],[0,1,"<a href=\"Entity\/ExportTableCollection.php.html#124\">Nzoom\\Export\\Entity\\ExportTableCollection::hasTables<\/a>"],[0,1,"<a href=\"Entity\/ExportTableCollection.php.html#134\">Nzoom\\Export\\Entity\\ExportTableCollection::getTablesWithRecords<\/a>"],[0,1,"<a href=\"Entity\/ExportTableCollection.php.html#146\">Nzoom\\Export\\Entity\\ExportTableCollection::getTableTypesWithRecords<\/a>"],[0,1,"<a href=\"Entity\/ExportTableCollection.php.html#157\">Nzoom\\Export\\Entity\\ExportTableCollection::getMetadata<\/a>"],[0,1,"<a href=\"Entity\/ExportTableCollection.php.html#167\">Nzoom\\Export\\Entity\\ExportTableCollection::setMetadata<\/a>"],[0,1,"<a href=\"Entity\/ExportTableCollection.php.html#179\">Nzoom\\Export\\Entity\\ExportTableCollection::getMetadataValue<\/a>"],[0,1,"<a href=\"Entity\/ExportTableCollection.php.html#190\">Nzoom\\Export\\Entity\\ExportTableCollection::setMetadataValue<\/a>"],[0,3,"<a href=\"Entity\/ExportTableCollection.php.html#200\">Nzoom\\Export\\Entity\\ExportTableCollection::validate<\/a>"],[0,2,"<a href=\"Entity\/ExportTableCollection.php.html#216\">Nzoom\\Export\\Entity\\ExportTableCollection::getTotalRecordCount<\/a>"],[0,1,"<a href=\"Entity\/ExportTableCollection.php.html#230\">Nzoom\\Export\\Entity\\ExportTableCollection::count<\/a>"],[0,1,"<a href=\"Entity\/ExportTableCollection.php.html#240\">Nzoom\\Export\\Entity\\ExportTableCollection::getIterator<\/a>"],[0,2,"<a href=\"Entity\/ExportTableCollection.php.html#252\">Nzoom\\Export\\Entity\\ExportTableCollection::toArray<\/a>"],[0,6,"<a href=\"Entity\/ExportTableCollection.php.html#274\">Nzoom\\Export\\Entity\\ExportTableCollection::fromArray<\/a>"],[100,1,"<a href=\"Entity\/ExportValue.php.html#58\">Nzoom\\Export\\Entity\\ExportValue::getValidTypes<\/a>"],[100,1,"<a href=\"Entity\/ExportValue.php.html#70\">Nzoom\\Export\\Entity\\ExportValue::__construct<\/a>"],[100,1,"<a href=\"Entity\/ExportValue.php.html#82\">Nzoom\\Export\\Entity\\ExportValue::getValue<\/a>"],[100,1,"<a href=\"Entity\/ExportValue.php.html#92\">Nzoom\\Export\\Entity\\ExportValue::setValue<\/a>"],[0,1,"<a href=\"Entity\/ExportValue.php.html#102\">Nzoom\\Export\\Entity\\ExportValue::getType<\/a>"],[28.57142857142857,3,"<a href=\"Entity\/ExportValue.php.html#113\">Nzoom\\Export\\Entity\\ExportValue::setType<\/a>"],[0,1,"<a href=\"Entity\/ExportValue.php.html#131\">Nzoom\\Export\\Entity\\ExportValue::getFormat<\/a>"],[0,1,"<a href=\"Entity\/ExportValue.php.html#142\">Nzoom\\Export\\Entity\\ExportValue::setFormat<\/a>"],[0,1,"<a href=\"Entity\/ExportValue.php.html#153\">Nzoom\\Export\\Entity\\ExportValue::isNull<\/a>"],[35,27,"<a href=\"Entity\/ExportValue.php.html#163\">Nzoom\\Export\\Entity\\ExportValue::validate<\/a>"],[0,15,"<a href=\"Entity\/ExportValue.php.html#196\">Nzoom\\Export\\Entity\\ExportValue::getFormattedValue<\/a>"],[0,8,"<a href=\"Entity\/ExportValue.php.html#233\">Nzoom\\Export\\Entity\\ExportValue::__toString<\/a>"],[0,3,"<a href=\"ExportActionFactory.php.html#55\">Nzoom\\Export\\ExportActionFactory::__construct<\/a>"],[0,1,"<a href=\"ExportActionFactory.php.html#85\">Nzoom\\Export\\ExportActionFactory::setModelName<\/a>"],[0,1,"<a href=\"ExportActionFactory.php.html#97\">Nzoom\\Export\\ExportActionFactory::setModelFactoryName<\/a>"],[0,1,"<a href=\"ExportActionFactory.php.html#110\">Nzoom\\Export\\ExportActionFactory::__invoke<\/a>"],[0,1,"<a href=\"ExportActionFactory.php.html#122\">Nzoom\\Export\\ExportActionFactory::createExportAction<\/a>"],[0,1,"<a href=\"ExportActionFactory.php.html#141\">Nzoom\\Export\\ExportActionFactory::prepareExportOptions<\/a>"],[0,3,"<a href=\"ExportActionFactory.php.html#177\">Nzoom\\Export\\ExportActionFactory::initializeFilterVisibility<\/a>"],[0,2,"<a href=\"ExportActionFactory.php.html#194\">Nzoom\\Export\\ExportActionFactory::firstOrZero<\/a>"],[0,4,"<a href=\"ExportActionFactory.php.html#209\">Nzoom\\Export\\ExportActionFactory::getPluginOptions<\/a>"],[0,4,"<a href=\"ExportActionFactory.php.html#280\">Nzoom\\Export\\ExportActionFactory::buildBaseExportOptions<\/a>"],[0,2,"<a href=\"ExportActionFactory.php.html#329\">Nzoom\\Export\\ExportActionFactory::getFormatOptions<\/a>"],[0,1,"<a href=\"ExportActionFactory.php.html#364\">Nzoom\\Export\\ExportActionFactory::getGroupTablesOptions<\/a>"],[0,2,"<a href=\"ExportActionFactory.php.html#397\">Nzoom\\Export\\ExportActionFactory::getDelimiterOptions<\/a>"],[0,1,"<a href=\"ExportService.php.html#69\">Nzoom\\Export\\ExportService::__construct<\/a>"],[0,1,"<a href=\"ExportService.php.html#84\">Nzoom\\Export\\ExportService::setModelName<\/a>"],[0,1,"<a href=\"ExportService.php.html#96\">Nzoom\\Export\\ExportService::setModelFactoryName<\/a>"],[0,1,"<a href=\"ExportService.php.html#110\">Nzoom\\Export\\ExportService::createExportAction<\/a>"],[0,1,"<a href=\"ExportService.php.html#127\">Nzoom\\Export\\ExportService::createExportData<\/a>"],[0,1,"<a href=\"ExportService.php.html#142\">Nzoom\\Export\\ExportService::createGeneratorFileStreamer<\/a>"],[0,3,"<a href=\"ExportService.php.html#156\">Nzoom\\Export\\ExportService::export<\/a>"],[0,2,"<a href=\"ExportService.php.html#181\">Nzoom\\Export\\ExportService::createTempStream<\/a>"],[0,3,"<a href=\"ExportService.php.html#201\">Nzoom\\Export\\ExportService::streamToBrowser<\/a>"],[0,2,"<a href=\"ExportService.php.html#232\">Nzoom\\Export\\ExportService::getFormatFactory<\/a>"],[0,1,"<a href=\"ExportService.php.html#247\">Nzoom\\Export\\ExportService::setFormatFactory<\/a>"],[0,2,"<a href=\"ExportService.php.html#259\">Nzoom\\Export\\ExportService::getAdapter<\/a>"],[0,1,"<a href=\"ExportService.php.html#273\">Nzoom\\Export\\ExportService::getSupportedFormats<\/a>"],[0,1,"<a href=\"ExportService.php.html#284\">Nzoom\\Export\\ExportService::isFormatSupported<\/a>"],[0,4,"<a href=\"ExportService.php.html#296\">Nzoom\\Export\\ExportService::getExportFilename<\/a>"],[0,4,"<a href=\"ExportService.php.html#326\">Nzoom\\Export\\ExportService::handleExportError<\/a>"],[0,1,"<a href=\"Factory\/ExportFormatFactory.php.html#44\">Nzoom\\Export\\Factory\\ExportFormatFactory::__construct<\/a>"],[0,3,"<a href=\"Factory\/ExportFormatFactory.php.html#60\">Nzoom\\Export\\Factory\\ExportFormatFactory::createAdapter<\/a>"],[0,3,"<a href=\"Factory\/ExportFormatFactory.php.html#94\">Nzoom\\Export\\Factory\\ExportFormatFactory::getAdapterClass<\/a>"],[0,6,"<a href=\"Factory\/ExportFormatFactory.php.html#112\">Nzoom\\Export\\Factory\\ExportFormatFactory::discoverAdapters<\/a>"],[0,4,"<a href=\"Factory\/ExportFormatFactory.php.html#148\">Nzoom\\Export\\Factory\\ExportFormatFactory::getClassNameFromFile<\/a>"],[0,2,"<a href=\"Factory\/ExportFormatFactory.php.html#171\">Nzoom\\Export\\Factory\\ExportFormatFactory::isValidAdapter<\/a>"],[0,3,"<a href=\"Factory\/ExportFormatFactory.php.html#186\">Nzoom\\Export\\Factory\\ExportFormatFactory::getSupportedFormats<\/a>"],[0,1,"<a href=\"Factory\/ExportFormatFactory.php.html#213\">Nzoom\\Export\\Factory\\ExportFormatFactory::isFormatSupported<\/a>"],[0,2,"<a href=\"Factory\/ExportFormatFactory.php.html#227\">Nzoom\\Export\\Factory\\ExportFormatFactory::createAdapterFromFilename<\/a>"],[0,1,"<a href=\"Provider\/ModelTableProvider.php.html#41\">Nzoom\\Export\\Provider\\ModelTableProvider::__construct<\/a>"],[0,1,"<a href=\"Provider\/ModelTableProvider.php.html#52\">Nzoom\\Export\\Provider\\ModelTableProvider::getDefaultOptions<\/a>"],[0,8,"<a href=\"Provider\/ModelTableProvider.php.html#65\">Nzoom\\Export\\Provider\\ModelTableProvider::getTablesForRecord<\/a>"],[0,6,"<a href=\"Provider\/ModelTableProvider.php.html#103\">Nzoom\\Export\\Provider\\ModelTableProvider::discoverGroupingVariables<\/a>"],[0,1,"<a href=\"Provider\/ModelTableProvider.php.html#135\">Nzoom\\Export\\Provider\\ModelTableProvider::getModelVariables<\/a>"],[0,3,"<a href=\"Provider\/ModelTableProvider.php.html#160\">Nzoom\\Export\\Provider\\ModelTableProvider::createTableFromGroupingData<\/a>"],[0,5,"<a href=\"Provider\/ModelTableProvider.php.html#202\">Nzoom\\Export\\Provider\\ModelTableProvider::getOrCreateTableHeader<\/a>"],[0,1,"<a href=\"Provider\/ModelTableProvider.php.html#237\">Nzoom\\Export\\Provider\\ModelTableProvider::formatTableName<\/a>"],[0,6,"<a href=\"Provider\/ModelTableProvider.php.html#252\">Nzoom\\Export\\Provider\\ModelTableProvider::guessColumnType<\/a>"],[0,4,"<a href=\"Provider\/ModelTableProvider.php.html#290\">Nzoom\\Export\\Provider\\ModelTableProvider::populateTableFromGroupingData<\/a>"],[0,4,"<a href=\"Provider\/ModelTableProvider.php.html#317\">Nzoom\\Export\\Provider\\ModelTableProvider::createRecordFromRowData<\/a>"],[0,13,"<a href=\"Provider\/ModelTableProvider.php.html#349\">Nzoom\\Export\\Provider\\ModelTableProvider::formatValue<\/a>"],[0,1,"<a href=\"Provider\/ModelTableProvider.php.html#388\">Nzoom\\Export\\Provider\\ModelTableProvider::getSupportedTableTypes<\/a>"],[0,1,"<a href=\"Provider\/ModelTableProvider.php.html#398\">Nzoom\\Export\\Provider\\ModelTableProvider::supportsTableType<\/a>"],[0,1,"<a href=\"Provider\/ModelTableProvider.php.html#407\">Nzoom\\Export\\Provider\\ModelTableProvider::getTableConfiguration<\/a>"],[0,2,"<a href=\"Provider\/ModelTableProvider.php.html#419\">Nzoom\\Export\\Provider\\ModelTableProvider::validateRecord<\/a>"],[0,1,"<a href=\"Streamer\/FileStreamer.php.html#60\">Nzoom\\Export\\Streamer\\FileStreamer::__construct<\/a>"],[0,2,"<a href=\"Streamer\/FileStreamer.php.html#71\">Nzoom\\Export\\Streamer\\FileStreamer::getHeaders<\/a>"],[0,1,"<a href=\"Streamer\/FileStreamer.php.html#85\">Nzoom\\Export\\Streamer\\FileStreamer::setHeaders<\/a>"],[0,1,"<a href=\"Streamer\/FileStreamer.php.html#96\">Nzoom\\Export\\Streamer\\FileStreamer::setTimeIncrement<\/a>"],[0,1,"<a href=\"Streamer\/FileStreamer.php.html#107\">Nzoom\\Export\\Streamer\\FileStreamer::setCacheExpires<\/a>"],[0,1,"<a href=\"Streamer\/FileStreamer.php.html#118\">Nzoom\\Export\\Streamer\\FileStreamer::setETag<\/a>"],[0,1,"<a href=\"Streamer\/FileStreamer.php.html#129\">Nzoom\\Export\\Streamer\\FileStreamer::setLastModified<\/a>"],[0,2,"<a href=\"Streamer\/FileStreamer.php.html#142\">Nzoom\\Export\\Streamer\\FileStreamer::stream<\/a>"],[100,0,"<a href=\"Streamer\/FileStreamer.php.html#168\">Nzoom\\Export\\Streamer\\FileStreamer::performStreaming<\/a>"],[0,2,"<a href=\"Streamer\/FileStreamer.php.html#175\">Nzoom\\Export\\Streamer\\FileStreamer::prepareForStreaming<\/a>"],[0,2,"<a href=\"Streamer\/FileStreamer.php.html#200\">Nzoom\\Export\\Streamer\\FileStreamer::setStreamingOptimizationHeaders<\/a>"],[0,2,"<a href=\"Streamer\/FileStreamer.php.html#225\">Nzoom\\Export\\Streamer\\FileStreamer::outputChunk<\/a>"],[0,1,"<a href=\"Streamer\/FileStreamer.php.html#243\">Nzoom\\Export\\Streamer\\FileStreamer::isClientConnected<\/a>"],[0,2,"<a href=\"Streamer\/FileStreamer.php.html#253\">Nzoom\\Export\\Streamer\\FileStreamer::increaseExecutionTime<\/a>"],[0,2,"<a href=\"Streamer\/FileStreamer.php.html#265\">Nzoom\\Export\\Streamer\\FileStreamer::cleanup<\/a>"],[0,1,"<a href=\"Streamer\/FileStreamer.php.html#278\">Nzoom\\Export\\Streamer\\FileStreamer::getFilename<\/a>"],[0,1,"<a href=\"Streamer\/FileStreamer.php.html#288\">Nzoom\\Export\\Streamer\\FileStreamer::getMimeType<\/a>"],[0,2,"<a href=\"Streamer\/GeneratorFileStreamer.php.html#36\">Nzoom\\Export\\Streamer\\GeneratorFileStreamer::__construct<\/a>"],[0,2,"<a href=\"Streamer\/GeneratorFileStreamer.php.html#56\">Nzoom\\Export\\Streamer\\GeneratorFileStreamer::initializeGenerator<\/a>"],[0,8,"<a href=\"Streamer\/GeneratorFileStreamer.php.html#68\">Nzoom\\Export\\Streamer\\GeneratorFileStreamer::performStreaming<\/a>"],[0,4,"<a href=\"Streamer\/GeneratorFileStreamer.php.html#98\">Nzoom\\Export\\Streamer\\GeneratorFileStreamer::cleanup<\/a>"],[0,2,"<a href=\"Streamer\/GeneratorFileStreamer.php.html#123\">Nzoom\\Export\\Streamer\\GeneratorFileStreamer::setTotalSize<\/a>"],[0,1,"<a href=\"Streamer\/GeneratorFileStreamer.php.html#140\">Nzoom\\Export\\Streamer\\GeneratorFileStreamer::getTotalSize<\/a>"],[0,1,"<a href=\"Streamer\/GeneratorFileStreamer.php.html#150\">Nzoom\\Export\\Streamer\\GeneratorFileStreamer::reset<\/a>"],[0,6,"<a href=\"Streamer\/PointerFileStreamer.php.html#36\">Nzoom\\Export\\Streamer\\PointerFileStreamer::__construct<\/a>"],[0,6,"<a href=\"Streamer\/PointerFileStreamer.php.html#71\">Nzoom\\Export\\Streamer\\PointerFileStreamer::performStreaming<\/a>"],[0,2,"<a href=\"Streamer\/PointerFileStreamer.php.html#93\">Nzoom\\Export\\Streamer\\PointerFileStreamer::cleanup<\/a>"],[0,1,"<a href=\"Streamer\/PointerFileStreamer.php.html#110\">Nzoom\\Export\\Streamer\\PointerFileStreamer::getChunkSize<\/a>"],[0,2,"<a href=\"Streamer\/PointerFileStreamer.php.html#121\">Nzoom\\Export\\Streamer\\PointerFileStreamer::setChunkSize<\/a>"],[0,1,"<a href=\"Streamer\/PointerFileStreamer.php.html#136\">Nzoom\\Export\\Streamer\\PointerFileStreamer::getTotalSize<\/a>"],[0,1,"<a href=\"Streamer\/StreamHeaders.php.html#25\">Nzoom\\Export\\Streamer\\StreamHeaders::addHeader<\/a>"],[0,1,"<a href=\"Streamer\/StreamHeaders.php.html#36\">Nzoom\\Export\\Streamer\\StreamHeaders::setHeaders<\/a>"],[0,1,"<a href=\"Streamer\/StreamHeaders.php.html#47\">Nzoom\\Export\\Streamer\\StreamHeaders::getHeader<\/a>"],[0,1,"<a href=\"Streamer\/StreamHeaders.php.html#57\">Nzoom\\Export\\Streamer\\StreamHeaders::getAll<\/a>"],[0,1,"<a href=\"Streamer\/StreamHeaders.php.html#67\">Nzoom\\Export\\Streamer\\StreamHeaders::clear<\/a>"],[0,1,"<a href=\"Streamer\/StreamHeaders.php.html#78\">Nzoom\\Export\\Streamer\\StreamHeaders::hasHeader<\/a>"],[0,1,"<a href=\"Streamer\/StreamHeaders.php.html#89\">Nzoom\\Export\\Streamer\\StreamHeaders::removeHeader<\/a>"],[0,1,"<a href=\"Streamer\/StreamHeaders.php.html#101\">Nzoom\\Export\\Streamer\\StreamHeaders::setFileContentHeaders<\/a>"],[0,2,"<a href=\"Streamer\/StreamHeaders.php.html#117\">Nzoom\\Export\\Streamer\\StreamHeaders::sanitizeFilename<\/a>"],[0,4,"<a href=\"Streamer\/StreamHeaders.php.html#138\">Nzoom\\Export\\Streamer\\StreamHeaders::prepareCacheHeaders<\/a>"],[0,3,"<a href=\"Streamer\/StreamHeaders.php.html#168\">Nzoom\\Export\\Streamer\\StreamHeaders::sendPreparedHeaders<\/a>"],[0,2,"<a href=\"Streamer\/StreamHeaders.php.html#189\">Nzoom\\Export\\Streamer\\StreamHeaders::flushHeaders<\/a>"],[0,1,"<a href=\"Streamer\/StreamHeaders.php.html#204\">Nzoom\\Export\\Streamer\\StreamHeaders::send304NotModified<\/a>"],[0,7,"<a href=\"Streamer\/StreamHeaders.php.html#223\">Nzoom\\Export\\Streamer\\StreamHeaders::handleCacheValidation<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>
