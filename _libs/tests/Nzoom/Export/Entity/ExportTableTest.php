<?php

namespace Tests\Nzoom\Export\Entity;

use PHPUnit\Framework\TestCase;
use Nzoom\Export\Entity\ExportTable;
use Nzoom\Export\Entity\ExportHeader;
use Nzoom\Export\Entity\ExportColumn;
use Nzoom\Export\Entity\ExportRecord;
use Nzoom\Export\Entity\ExportValue;

class ExportTableTest extends TestCase
{
    public function testBasicTableCreation(): void
    {
        $header = new ExportHeader();
        $header->addColumn(new ExportColumn('Name', 'name', 'Name'));
        $header->addColumn(new ExportColumn('Price', 'price', 'Price'));

        $table = new ExportTable('products', 'Product List', $header, 'customer_123');

        $this->assertEquals('products', $table->getTableType());
        $this->assertEquals('Product List', $table->getTableName());
        $this->assertEquals('customer_123', $table->getParentReference());
        $this->assertSame($header, $table->getHeader());
        $this->assertFalse($table->hasRecords());
        $this->assertEquals(0, $table->count());
    }

    public function testAddingRecords(): void
    {
        $header = new ExportHeader();
        $header->addColumn(new ExportColumn('Name', 'name', 'Name'));
        $header->addColumn(new ExportColumn('Price', 'price', 'Price'));

        $table = new ExportTable('products', 'Product List', $header);

        // Create and add first record
        $record1 = new ExportRecord();
        $record1->addValue('name', 'Laptop', ExportValue::TYPE_STRING);
        $record1->addValue('price', 999.99, ExportValue::TYPE_FLOAT);
        $table->addRecord($record1);

        // Create and add second record
        $record2 = new ExportRecord();
        $record2->addValue('name', 'Mouse', ExportValue::TYPE_STRING);
        $record2->addValue('price', 29.99, ExportValue::TYPE_FLOAT);
        $table->addRecord($record2);

        $this->assertTrue($table->hasRecords());
        $this->assertEquals(2, $table->count());

        $records = $table->getRecords();
        $this->assertCount(2, $records);
        $this->assertSame($record1, $records[0]);
        $this->assertSame($record2, $records[1]);
    }

    public function testTableMetadata(): void
    {
        $metadata = ['source' => 'database', 'table' => 'products'];
        $table = new ExportTable('products', 'Product List', null, null, $metadata);

        $this->assertEquals($metadata, $table->getMetadata());
        $this->assertEquals('database', $table->getMetadataValue('source'));
        $this->assertEquals('products', $table->getMetadataValue('table'));
        $this->assertNull($table->getMetadataValue('nonexistent'));
        $this->assertEquals('default', $table->getMetadataValue('nonexistent', 'default'));

        $table->setMetadataValue('new_key', 'new_value');
        $this->assertEquals('new_value', $table->getMetadataValue('new_key'));
    }

    public function testTableToArray(): void
    {
        $header = new ExportHeader();
        $header->addColumn(new ExportColumn('Name', 'name', 'Name'));
        $header->addColumn(new ExportColumn('Price', 'price', 'Price'));

        $table = new ExportTable('products', 'Product List', $header);

        $record1 = new ExportRecord();
        $record1->addValue('name', 'Laptop', ExportValue::TYPE_STRING);
        $record1->addValue('price', 999.99, ExportValue::TYPE_FLOAT);
        $table->addRecord($record1);

        $record2 = new ExportRecord();
        $record2->addValue('name', 'Mouse', ExportValue::TYPE_STRING);
        $record2->addValue('price', 29.99, ExportValue::TYPE_FLOAT);
        $table->addRecord($record2);

        $arrayData = $table->toArray();

        $expected = [
            ['Name', 'Price'], // Header row
            ['Laptop', 999.99], // First record
            ['Mouse', 29.99]    // Second record
        ];

        $this->assertEquals($expected, $arrayData);
    }

    public function testTableIteration(): void
    {
        $header = new ExportHeader();
        $header->addColumn(new ExportColumn('Name', 'name', 'Name'));

        $table = new ExportTable('products', 'Product List', $header);

        $record1 = new ExportRecord();
        $record1->addValue('name', 'Item 1', ExportValue::TYPE_STRING);
        $table->addRecord($record1);

        $record2 = new ExportRecord();
        $record2->addValue('name', 'Item 2', ExportValue::TYPE_STRING);
        $table->addRecord($record2);

        $iteratedRecords = [];
        foreach ($table as $record) {
            $iteratedRecords[] = $record;
        }

        $this->assertCount(2, $iteratedRecords);
        $this->assertSame($record1, $iteratedRecords[0]);
        $this->assertSame($record2, $iteratedRecords[1]);
    }

    public function testRecordValidation(): void
    {
        $header = new ExportHeader();
        $header->addColumn(new ExportColumn('Name', 'name', 'Name'));
        $header->addColumn(new ExportColumn('Price', 'price', 'Price'));

        $table = new ExportTable('products', 'Product List', $header);

        // Valid record
        $validRecord = new ExportRecord();
        $validRecord->addValue('name', 'Laptop', ExportValue::TYPE_STRING);
        $validRecord->addValue('price', 999.99, ExportValue::TYPE_FLOAT);

        // This should not throw an exception
        $table->addRecord($validRecord, true);
        $this->assertEquals(1, $table->count());

        // Invalid record (missing a value)
        $invalidRecord = new ExportRecord();
        $invalidRecord->addValue('name', 'Mouse', ExportValue::TYPE_STRING);
        // Missing price value

        $this->expectException(\InvalidArgumentException::class);
        $table->addRecord($invalidRecord, true);
    }

    public function testClearRecords(): void
    {
        $header = new ExportHeader();
        $header->addColumn(new ExportColumn('Name', 'name', 'Name'));

        $table = new ExportTable('products', 'Product List', $header);

        $record = new ExportRecord();
        $record->addValue('name', 'Item', ExportValue::TYPE_STRING);
        $table->addRecord($record);

        $this->assertTrue($table->hasRecords());
        $this->assertEquals(1, $table->count());

        $table->clearRecords();

        $this->assertFalse($table->hasRecords());
        $this->assertEquals(0, $table->count());
        $this->assertEmpty($table->getRecords());
    }

    public function testSetRecords(): void
    {
        $header = new ExportHeader();
        $header->addColumn(new ExportColumn('Name', 'name', 'Name'));

        $table = new ExportTable('products', 'Product List', $header);

        $record1 = new ExportRecord();
        $record1->addValue('name', 'Item 1', ExportValue::TYPE_STRING);

        $record2 = new ExportRecord();
        $record2->addValue('name', 'Item 2', ExportValue::TYPE_STRING);

        $records = [$record1, $record2];
        $table->setRecords($records);

        $this->assertEquals(2, $table->count());
        $this->assertEquals($records, $table->getRecords());
    }

    public function testTableValidation(): void
    {
        $header = new ExportHeader();
        $header->addColumn(new ExportColumn('Name', 'name', 'Name'));

        $table = new ExportTable('products', 'Product List', $header);

        // Add valid record
        $validRecord = new ExportRecord();
        $validRecord->addValue('name', 'Item', ExportValue::TYPE_STRING);
        $table->addRecord($validRecord, false); // Skip validation during add

        $this->assertTrue($table->validate());

        // Add invalid record
        $invalidRecord = new ExportRecord();
        $invalidRecord->addValue('name', 'Item', ExportValue::TYPE_STRING);
        $invalidRecord->addValue('extra', 'Extra value', ExportValue::TYPE_STRING); // Extra value
        $table->addRecord($invalidRecord, false); // Skip validation during add

        $this->assertFalse($table->validate());
    }
}
