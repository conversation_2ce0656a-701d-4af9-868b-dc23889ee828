<?php

namespace Tests\Nzoom\Export\Provider;

use Tests\Nzoom\Export\ExportTestCase;
use Tests\Nzoom\TestHelpers\RegistryMock;
use Nzoom\Export\Provider\ModelTableProvider;
use Nzoom\Export\Entity\ExportValue;
use Nzoom\Export\Entity\ExportTableCollection;
use Nzoom\Export\Entity\ExportTable;

class ModelTableProviderTest extends ExportTestCase
{
    private RegistryMock $registry;
    private $mockLogger;

    protected function setUp(): void
    {
        parent::setUp();

        // Include global mock classes
        require_once __DIR__ . '/../../TestHelpers/GlobalMocks.php';

        // Create registry mock
        $this->registry = new RegistryMock();

        // Create mock logger
        $this->mockLogger = new class {
            public function warning($message) {
                // Mock logger - do nothing
            }
        };
        $this->registry->set('logger', $this->mockLogger);
    }

    /**
     * Create a mock model with table data
     */
    private function createMockModelWithTables($data = [], $purchaseData = [], $contactData = [], $noteData = [])
    {
        // Create an anonymous class that extends Model and adds the methods we need
        return new class($data, $purchaseData, $contactData, $noteData) extends \Model {
            private $purchaseData;
            private $contactData;
            private $noteData;

            public function __construct($data, $purchaseData, $contactData, $noteData)
            {
                parent::__construct($data);
                $this->purchaseData = $purchaseData;
                $this->contactData = $contactData;
                $this->noteData = $noteData;
            }

            public function getPurchases()
            {
                return $this->purchaseData;
            }

            public function getContacts()
            {
                return $this->contactData;
            }

            public function getNotes()
            {
                return $this->noteData;
            }

            public function getOrders()
            {
                return [];
            }
        };
    }

    public function testBasicProviderCreation(): void
    {
        $config = [
            'table_types' => [
                'purchases' => [
                    'name' => 'Purchase History',
                    'relation_method' => 'getPurchases',
                    'columns' => [
                        ['var_name' => 'item', 'label' => 'Item', 'field' => 'item_name']
                    ]
                ]
            ]
        ];

        $provider = new ModelTableProvider($this->registry, $config);

        $this->assertEquals(['purchases'], $provider->getSupportedTableTypes());
        $this->assertTrue($provider->supportsTableType('purchases'));
        $this->assertFalse($provider->supportsTableType('nonexistent'));
    }

    public function testGetTableConfiguration(): void
    {
        $config = [
            'table_types' => [
                'orders' => [
                    'name' => 'Order History',
                    'relation_method' => 'getOrders',
                    'columns' => [
                        ['var_name' => 'order_id', 'label' => 'Order ID', 'field' => 'id', 'type' => 'integer']
                    ]
                ]
            ]
        ];

        $provider = new ModelTableProvider($this->registry, $config);
        $tableConfig = $provider->getTableConfiguration('orders');

        $this->assertEquals('Order History', $tableConfig['name']);
        $this->assertEquals('getOrders', $tableConfig['relation_method']);
        $this->assertCount(1, $tableConfig['columns']);
        $this->assertEquals('order_id', $tableConfig['columns'][0]['var_name']);
    }

    public function testGetTablesForNonModelRecord(): void
    {
        $provider = new ModelTableProvider($this->registry, []);
        $collection = $provider->getTablesForRecord('not a model');

        $this->assertInstanceOf(ExportTableCollection::class, $collection);
        $this->assertFalse($collection->hasTables());
    }

    public function testGetTablesForModelWithoutConfiguration(): void
    {
        $mockModel = $this->createMockModelWithTables(['id' => 123]);
        $provider = new ModelTableProvider($this->registry, []);

        $collection = $provider->getTablesForRecord($mockModel);

        $this->assertInstanceOf(ExportTableCollection::class, $collection);
        $this->assertFalse($collection->hasTables());
    }

    public function testGetTablesForModelWithValidConfiguration(): void
    {
        $purchaseData = [
            ['item_name' => 'Laptop', 'price' => 999.99],
            ['item_name' => 'Mouse', 'price' => 29.99]
        ];
        $mockModel = $this->createMockModelWithTables(['id' => 123], $purchaseData);

        $config = [
            'table_types' => [
                'purchases' => [
                    'name' => 'Purchase History',
                    'relation_method' => 'getPurchases',
                    'columns' => [
                        ['var_name' => 'item', 'label' => 'Item', 'field' => 'item_name', 'type' => ExportValue::TYPE_STRING],
                        ['var_name' => 'price', 'label' => 'Price', 'field' => 'price', 'type' => ExportValue::TYPE_FLOAT]
                    ]
                ]
            ]
        ];

        $provider = new ModelTableProvider($this->registry, $config);
        $collection = $provider->getTablesForRecord($mockModel, ['table_types' => ['purchases']]);

        $this->assertTrue($collection->hasTables());
        $this->assertTrue($collection->hasTable('purchases'));
        
        $table = $collection->getTable('purchases');
        $this->assertInstanceOf(ExportTable::class, $table);
        $this->assertEquals('purchases', $table->getTableType());
        $this->assertEquals('Purchase History', $table->getTableName());
        $this->assertEquals('123', $table->getParentReference());
        $this->assertTrue($table->hasRecords());
        $this->assertEquals(2, $table->count());
    }

    public function testGetTablesWithMultipleTableTypes(): void
    {
        $purchaseData = [['item_name' => 'Laptop', 'price' => 999.99]];
        $contactData = [['type' => 'email', 'value' => '<EMAIL>']];
        $mockModel = $this->createMockModelWithTables(['id' => 456], $purchaseData, $contactData);

        $config = [
            'table_types' => [
                'purchases' => [
                    'name' => 'Purchase History',
                    'relation_method' => 'getPurchases',
                    'columns' => [
                        ['var_name' => 'item', 'label' => 'Item', 'field' => 'item_name', 'type' => ExportValue::TYPE_STRING]
                    ]
                ],
                'contacts' => [
                    'name' => 'Contact Information',
                    'relation_method' => 'getContacts',
                    'columns' => [
                        ['var_name' => 'type', 'label' => 'Type', 'field' => 'type', 'type' => ExportValue::TYPE_STRING],
                        ['var_name' => 'value', 'label' => 'Value', 'field' => 'value', 'type' => ExportValue::TYPE_STRING]
                    ]
                ]
            ]
        ];

        $provider = new ModelTableProvider($this->registry, $config);
        $collection = $provider->getTablesForRecord($mockModel, ['table_types' => ['purchases', 'contacts']]);

        $this->assertTrue($collection->hasTables());
        $this->assertEquals(2, $collection->count());
        $this->assertTrue($collection->hasTable('purchases'));
        $this->assertTrue($collection->hasTable('contacts'));

        $purchaseTable = $collection->getTable('purchases');
        $contactTable = $collection->getTable('contacts');

        $this->assertEquals(1, $purchaseTable->count());
        $this->assertEquals(1, $contactTable->count());
    }

    public function testGetTablesWithEmptyTablesIncluded(): void
    {
        $mockModel = $this->createMockModelWithTables(['id' => 789], []); // Empty purchase data

        $config = [
            'table_types' => [
                'purchases' => [
                    'name' => 'Purchase History',
                    'relation_method' => 'getPurchases',
                    'columns' => [
                        ['var_name' => 'item', 'label' => 'Item', 'field' => 'item_name', 'type' => ExportValue::TYPE_STRING]
                    ]
                ]
            ]
        ];

        $provider = new ModelTableProvider($this->registry, $config);
        
        // Test without including empty tables
        $collection1 = $provider->getTablesForRecord($mockModel, [
            'table_types' => ['purchases'],
            'include_empty_tables' => false
        ]);
        $this->assertFalse($collection1->hasTables());

        // Test with including empty tables
        $collection2 = $provider->getTablesForRecord($mockModel, [
            'table_types' => ['purchases'],
            'include_empty_tables' => true
        ]);
        $this->assertTrue($collection2->hasTables());
        $this->assertEquals(0, $collection2->getTable('purchases')->count());
    }

    public function testGetTablesWithMaxRecordsLimit(): void
    {
        // Create more records than the limit
        $manyRecords = [];
        for ($i = 1; $i <= 5; $i++) {
            $manyRecords[] = ['item_name' => "Item {$i}", 'price' => $i * 10];
        }
        $mockModel = $this->createMockModelWithTables(['id' => 999], $manyRecords);

        $config = [
            'table_types' => [
                'purchases' => [
                    'name' => 'Purchase History',
                    'relation_method' => 'getPurchases',
                    'columns' => [
                        ['var_name' => 'item', 'label' => 'Item', 'field' => 'item_name', 'type' => ExportValue::TYPE_STRING]
                    ]
                ]
            ]
        ];

        $provider = new ModelTableProvider($this->registry, $config);
        $collection = $provider->getTablesForRecord($mockModel, [
            'table_types' => ['purchases'],
            'max_records_per_table' => 3
        ]);

        $table = $collection->getTable('purchases');
        $this->assertEquals(3, $table->count()); // Should be limited to 3 records
    }

    public function testValidateRecord(): void
    {
        $config = [
            'table_types' => [
                'orders' => [
                    'name' => 'Order History',
                    'relation_method' => 'getOrders',
                    'columns' => [
                        ['var_name' => 'order_id', 'label' => 'Order ID', 'field' => 'id', 'type' => ExportValue::TYPE_INTEGER]
                    ]
                ]
            ]
        ];

        $provider = new ModelTableProvider($this->registry, $config);

        // Test with non-model
        $this->assertFalse($provider->validateRecord('not a model', ['orders']));

        // Test with model that has the required method
        $validModel = $this->createMockModelWithTables(['id' => 123]);
        $this->assertTrue($provider->validateRecord($validModel, ['orders']));

        // Test with model that doesn't have the required method (using regular Model)
        $invalidModel = new \Model(['id' => 456]);
        $this->assertFalse($provider->validateRecord($invalidModel, ['orders']));

        // Test with empty requested table types (should return true)
        $this->assertTrue($provider->validateRecord($validModel, []));
    }

    public function testErrorHandlingInTableExtraction(): void
    {
        // Create a mock model that throws an exception
        $mockModel = new class(['id' => 123]) extends \Model {
            public function getPurchases()
            {
                throw new \Exception('Database error');
            }
        };

        $config = [
            'table_types' => [
                'purchases' => [
                    'name' => 'Purchase History',
                    'relation_method' => 'getPurchases',
                    'columns' => [
                        ['var_name' => 'item', 'label' => 'Item', 'field' => 'item_name', 'type' => ExportValue::TYPE_STRING]
                    ]
                ]
            ]
        ];

        $provider = new ModelTableProvider($this->registry, $config);
        $collection = $provider->getTablesForRecord($mockModel, ['table_types' => ['purchases']]);

        // Should return empty collection when extraction fails
        $this->assertFalse($collection->hasTables());
    }


}
