# ModelTableProvider Integration Guide

## Overview

The `ModelTableProvider` is now seamlessly integrated into the `DataFactory`, making table export as simple as configuring the factory. This integration ensures that table extraction happens automatically during record creation, supporting both eager and lazy loading scenarios.

## Integration Architecture

```
DataFactory
├── setTableProvider() - Configure table provider
├── withModelTableProvider() - Fluent configuration
├── createWithTables() - One-step creation with tables
├── createRecordFromModel() - Enhanced to extract tables
└── extractTablesForRecord() - Automatic table extraction
```

## Usage Patterns

### 1. **Simple One-Step Approach** ⭐ **RECOMMENDED**

```php
$dataFactory = new DataFactory($registry);

$exportData = $dataFactory->createWithTables($models, $outlook, [
    'table_types' => [
        'purchases' => [
            'name' => 'Purchase Items',
            'relation_method' => 'getPurchaseItems',
            'columns' => [
                ['var_name' => 'item_name', 'label' => 'Item Name', 'field' => 'name'],
                ['var_name' => 'price', 'label' => 'Price', 'field' => 'price', 'type' => 'float']
            ]
        ]
    ]
]);

// Tables are automatically included!
$adapter->export('export.xlsx', 'xlsx', $exportData);
```

### 2. **Fluent Configuration Approach**

```php
$dataFactory = new DataFactory($registry);

$exportData = $dataFactory
    ->withModelTableProvider([
        'table_types' => [
            'contacts' => [
                'name' => 'Contact Info',
                'relation_method' => 'getContacts',
                'columns' => [
                    ['var_name' => 'type', 'label' => 'Type', 'field' => 'contact_type'],
                    ['var_name' => 'value', 'label' => 'Value', 'field' => 'contact_value']
                ]
            ]
        ]
    ])
    ->__invoke($models, $outlook); // Or just call the factory

$adapter->export('export.xlsx', 'xlsx', $exportData);
```

### 3. **Streaming with Tables**

```php
$dataFactory = new DataFactory($registry);

// Configure table provider
$dataFactory->withModelTableProvider([
    'table_types' => [
        'orders' => [
            'name' => 'Order History',
            'relation_method' => 'getOrders',
            'columns' => [
                ['var_name' => 'order_id', 'label' => 'Order ID', 'field' => 'id']
            ]
        ]
    ]
]);

// Create streaming export with tables
$exportData = $dataFactory->createStreaming($factoryClass, $filters, $outlook, 500);

$adapter->export('streaming_export.xlsx', 'xlsx', $exportData);
```

### 4. **Conditional Table Support**

```php
$dataFactory = new DataFactory($registry);

$includeDetails = $_GET['include_details'] ?? false;

if ($includeDetails) {
    $exportData = $dataFactory->createWithTables($models, $outlook, [
        'table_types' => [
            'purchases' => [...],
            'notes' => [...]
        ]
    ]);
} else {
    $exportData = $dataFactory($models, $outlook); // No tables
}

$adapter->export('export.xlsx', 'xlsx', $exportData);
```

## Configuration Structure

### Table Type Configuration

```php
'table_types' => [
    'table_identifier' => [
        'name' => 'Human Readable Name',           // Required: Worksheet name
        'relation_method' => 'getRelatedData',     // Required: Model method to call
        'columns' => [                             // Required: Column definitions
            [
                'var_name' => 'column_id',         // Required: Unique column identifier
                'label' => 'Column Label',         // Required: Display name
                'field' => 'model_field',          // Required: Field in related data
                'type' => 'string',                // Optional: Data type
                'format' => 'format_spec'          // Optional: Format specification
            ]
        ]
    ]
]
```

### Global Configuration Options

```php
[
    'table_types' => [...],                    // Table definitions
    'max_records_per_table' => 1000,          // Limit records per table
    'include_empty_tables' => false,          // Include tables with no data
    'date_format' => 'd.m.Y',                 // Default date format
    'datetime_format' => 'd.m.Y H:i'          // Default datetime format
]
```

## Model Requirements

Your Model classes need to implement the relation methods specified in the configuration:

```php
class Customer extends Model
{
    /**
     * Get purchase items for this customer
     * @return array Array of purchase data
     */
    public function getPurchaseItems()
    {
        // Return array of objects/arrays with the fields specified in columns
        return [
            ['name' => 'Laptop', 'price' => 999.99, 'created_at' => '2024-01-15'],
            ['name' => 'Mouse', 'price' => 29.99, 'created_at' => '2024-01-20']
        ];
    }

    /**
     * Get contact information for this customer
     * @return array Array of contact data
     */
    public function getContacts()
    {
        return [
            ['contact_type' => 'email', 'contact_value' => '<EMAIL>'],
            ['contact_type' => 'phone', 'contact_value' => '+1234567890']
        ];
    }
}
```

## Integration Benefits

### ✅ **Automatic Processing**
- Tables are extracted during record creation
- No manual iteration through records needed
- Works with both eager and lazy loading

### ✅ **Memory Efficient**
- Tables are processed as records are created
- Supports streaming for large datasets
- Configurable limits prevent memory issues

### ✅ **Error Resilient**
- Failed table extraction doesn't break export
- Errors are logged but export continues
- Graceful handling of missing relations

### ✅ **Backward Compatible**
- Existing code works unchanged
- Tables are opt-in only
- No performance impact when disabled

## Migration from Manual Approach

### Before (Manual)
```php
$dataFactory = new DataFactory($registry);
$exportData = $dataFactory($models, $outlook);

$exportData->enableTables([...]);
$tableProvider = new ModelTableProvider($registry, [...]);

foreach ($exportData as $record) {
    $model = $record->getMetadataValue('source_model');
    $tables = $tableProvider->getTablesForRecord($model);
    $record->setTableCollection($tables);
}

$adapter->export('export.xlsx', 'xlsx', $exportData);
```

### After (Integrated)
```php
$dataFactory = new DataFactory($registry);
$exportData = $dataFactory->createWithTables($models, $outlook, [...]);

$adapter->export('export.xlsx', 'xlsx', $exportData);
```

## Performance Considerations

- **Table Extraction**: Happens once per record during creation
- **Memory Usage**: Controlled by `max_records_per_table` setting
- **Database Queries**: One query per relation method per record
- **Streaming**: Fully supported - tables extracted per chunk

## Error Handling

The integration includes comprehensive error handling:

- **Missing Relations**: Logged as warnings, export continues
- **Invalid Data**: Validation errors logged, empty tables created
- **Provider Errors**: Caught and logged, record processed without tables
- **Configuration Errors**: Clear error messages for invalid config

## Best Practices

1. **Use `createWithTables()`** for new implementations
2. **Configure limits** to prevent memory issues
3. **Validate models** before processing if needed
4. **Test with small datasets** first
5. **Monitor logs** for extraction errors
6. **Use streaming** for large datasets

## Example Controller Integration

```php
class ExportController
{
    public function exportCustomers()
    {
        $models = Customer::search($this->registry, $filters);
        $outlook = $this->getOutlook();
        
        $dataFactory = new DataFactory($this->registry);
        
        $includeDetails = $this->request->get('include_details', false);
        
        if ($includeDetails) {
            $exportData = $dataFactory->createWithTables($models, $outlook, [
                'table_types' => [
                    'purchases' => [
                        'name' => 'Purchase History',
                        'relation_method' => 'getPurchases',
                        'columns' => [
                            ['var_name' => 'item', 'label' => 'Item', 'field' => 'item_name'],
                            ['var_name' => 'amount', 'label' => 'Amount', 'field' => 'total', 'type' => 'float']
                        ]
                    ]
                ]
            ]);
        } else {
            $exportData = $dataFactory($models, $outlook);
        }
        
        $adapter = new ExcelExportFormatAdapter($this->registry, 'customers', 'export');
        $adapter->export('php://output', 'xlsx', $exportData);
    }
}
```

This integration makes table export a first-class feature that's as easy to use as regular exports!
