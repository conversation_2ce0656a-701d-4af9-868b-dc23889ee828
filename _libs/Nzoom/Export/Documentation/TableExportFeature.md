# Export Table Support Feature

## Overview

The Export Table Support feature allows you to include related table data alongside main export records. This is particularly useful for exporting complex data structures where each main record has associated sub-records (like customers with their purchase history, orders with line items, etc.).

## Key Features

- **Opt-in Design**: Table export is disabled by default and must be explicitly enabled
- **Non-breaking**: Existing export functionality remains unchanged
- **Streaming Compatible**: Works with both eager and lazy loading
- **Excel Multi-sheet**: Creates separate worksheets for each table type in Excel exports
- **Type Safety**: Tables have their own headers and strongly-typed columns
- **Flexible Configuration**: Supports various data sources and extraction methods

## Core Components

### 1. ExportTable
Represents a single table with its own header and records.

```php
$table = new ExportTable('purchases', 'Purchase History', $header, 'customer_123');
$table->addRecord($record);
```

### 2. ExportTableCollection
Manages multiple tables for a single record.

```php
$collection = new ExportTableCollection();
$collection->addTable($purchaseTable);
$collection->addTable($contactTable);
```

### 3. ExportTableProviderInterface
Defines how to extract table data from source records.

```php
interface ExportTableProviderInterface {
    public function getTablesForRecord($record, array $options = []): ExportTableCollection;
    public function getSupportedTableTypes(): array;
    public function supportsTableType(string $tableType): bool;
}
```

### 4. Enhanced ExportData
Supports table configuration and enablement.

```php
$exportData->enableTables([
    'table_types' => ['purchases', 'contacts'],
    'max_records_per_table' => 500
]);
```

### 5. Enhanced ExportRecord
Can contain an optional table collection.

```php
$record->setTableCollection($tableCollection);
$record->hasTables(); // Check if record has tables
$record->getTable('purchases'); // Get specific table
```

## Usage Patterns

### Basic Setup

1. **Enable Tables on ExportData**
```php
$exportData->enableTables([
    'table_types' => ['purchases', 'contacts'],
    'max_records_per_table' => 1000,
    'include_empty_tables' => false
]);
```

2. **Configure Table Provider**
```php
$tableProvider = new ModelTableProvider($registry, [
    'table_types' => [
        'purchases' => [
            'name' => 'Purchase Items',
            'relation_method' => 'getPurchaseItems',
            'columns' => [
                [
                    'var_name' => 'item_name',
                    'label' => 'Item Name',
                    'field' => 'name',
                    'type' => ExportValue::TYPE_STRING
                ],
                [
                    'var_name' => 'price',
                    'label' => 'Price',
                    'field' => 'price',
                    'type' => ExportValue::TYPE_FLOAT,
                    'format' => '2'
                ]
            ]
        ]
    ]
]);
```

3. **Add Tables to Records**
```php
foreach ($exportData as $record) {
    $originalModel = $record->getMetadataValue('source_model');
    if ($originalModel) {
        $tableCollection = $tableProvider->getTablesForRecord($originalModel);
        $record->setTableCollection($tableCollection);
    }
}
```

4. **Export with Tables**
```php
$adapter = new ExcelExportFormatAdapter($registry, 'customers', 'export');
$adapter->export('export_with_tables.xlsx', 'xlsx', $exportData);
```

### Manual Table Creation

```php
// Create table header
$header = new ExportHeader();
$header->addColumn(new ExportColumn('Item', 'item', 'Item'));
$header->addColumn(new ExportColumn('Price', 'price', 'Price'));

// Create table
$table = new ExportTable('purchases', 'Purchase History', $header);

// Add records
$record = new ExportRecord();
$record->addValue('item', 'Laptop', ExportValue::TYPE_STRING);
$record->addValue('price', 1299.99, ExportValue::TYPE_FLOAT, '2');
$table->addRecord($record);

// Add to main record
$mainRecord->addTable($table);
```

## Configuration Options

### ExportData Table Configuration

- `table_types`: Array of table type identifiers to include
- `max_records_per_table`: Maximum records per table (default: 1000)
- `include_empty_tables`: Whether to include tables with no records (default: false)

### Table Provider Configuration

Each table type can be configured with:

- `name`: Human-readable table name
- `relation_method`: Method to call on source record to get related data
- `columns`: Array of column definitions

### Column Configuration

- `var_name`: Variable name for the column
- `label`: Display label
- `field`: Field name in source data
- `type`: Data type (string, integer, float, date, datetime, boolean)
- `format`: Optional format specification

## Excel Output

When tables are enabled, the Excel export creates:

1. **Main Sheet**: Contains the primary export data (as usual)
2. **Table Sheets**: One worksheet per table type containing:
   - Combined data from all records of that table type
   - Proper headers and formatting
   - Same styling as main sheet

### Worksheet Naming

- Table worksheets are named using the table's `name` property
- Names are sanitized to comply with Excel requirements (max 31 chars, no special chars)

## Performance Considerations

- **Memory Usage**: Tables increase memory usage; use `max_records_per_table` to limit
- **Processing Time**: More data means longer export times
- **Streaming**: Works with lazy loading for main records, but table data is loaded eagerly
- **Garbage Collection**: Automatic cleanup during processing

## Error Handling

- **Invalid Tables**: Logged but don't stop export
- **Missing Relations**: Gracefully handled with empty tables
- **Validation Errors**: Can validate records before processing
- **Excel Limits**: Worksheet creation errors are logged

## Backward Compatibility

- **Default Behavior**: Tables are disabled by default
- **Existing Code**: No changes required for existing exports
- **API Stability**: All existing methods work unchanged

## Best Practices

1. **Validate First**: Use `validateRecord()` to check if records support required tables
2. **Limit Data**: Set reasonable `max_records_per_table` limits
3. **Handle Errors**: Check logs for table extraction issues
4. **Test Memory**: Monitor memory usage with large datasets
5. **Conditional Use**: Only enable tables when needed (e.g., user option)

## Example Use Cases

- **Customer Export**: Include purchase history, contact information, notes
- **Order Export**: Include line items, payments, shipping details
- **Invoice Export**: Include invoice items, payments, adjustments
- **Product Export**: Include variants, pricing history, inventory levels
- **Project Export**: Include tasks, team members, time entries

## Future Enhancements

- Support for other export formats (CSV with multiple files, JSON with nested structure)
- Table relationships and foreign keys
- Aggregated table data (sums, counts, averages)
- Custom table processors for complex data transformations
- Table-level formatting and styling options
