<?php

namespace Nzoom\Export\Examples;

use Nzoom\Export\Entity\ExportData;
use Nzoom\Export\Entity\ExportHeader;
use Nzoom\Export\Entity\ExportColumn;
use Nzoom\Export\Entity\ExportRecord;
use Nzoom\Export\Entity\ExportValue;
use Nzoom\Export\Entity\ExportTable;
use Nzoom\Export\Entity\ExportTableCollection;
use Nzoom\Export\Provider\ModelTableProvider;
use Nzoom\Export\Adapter\ExcelExportFormatAdapter;

/**
 * Class TableExportUsage
 *
 * Examples of how to use the table export functionality
 */
class TableExportUsage
{
    /**
     * Example 1: Basic table export setup
     */
    public static function basicTableExport(\Registry $registry, array $models, \Outlook $outlook)
    {
        // Create export data as usual
        $dataFactory = new \Nzoom\Export\DataFactory($registry);
        $exportData = $dataFactory($models, $outlook);

        // Enable table export with configuration
        $exportData->enableTables([
            'table_types' => ['purchases', 'contacts'],
            'max_records_per_table' => 500,
            'include_empty_tables' => false
        ]);

        // Create table provider
        $tableProvider = new ModelTableProvider($registry, [
            'table_types' => [
                'purchases' => [
                    'name' => 'Purchase Items',
                    'relation_method' => 'getPurchaseItems',
                    'columns' => [
                        [
                            'var_name' => 'item_name',
                            'label' => 'Item Name',
                            'field' => 'name',
                            'type' => ExportValue::TYPE_STRING
                        ],
                        [
                            'var_name' => 'quantity',
                            'label' => 'Quantity',
                            'field' => 'quantity',
                            'type' => ExportValue::TYPE_INTEGER
                        ],
                        [
                            'var_name' => 'price',
                            'label' => 'Price',
                            'field' => 'price',
                            'type' => ExportValue::TYPE_FLOAT,
                            'format' => '2'
                        ],
                        [
                            'var_name' => 'purchase_date',
                            'label' => 'Purchase Date',
                            'field' => 'created_at',
                            'type' => ExportValue::TYPE_DATE,
                            'format' => 'd.m.Y'
                        ]
                    ]
                ],
                'contacts' => [
                    'name' => 'Contact Information',
                    'relation_method' => 'getContacts',
                    'columns' => [
                        [
                            'var_name' => 'contact_type',
                            'label' => 'Type',
                            'field' => 'type',
                            'type' => ExportValue::TYPE_STRING
                        ],
                        [
                            'var_name' => 'contact_value',
                            'label' => 'Value',
                            'field' => 'value',
                            'type' => ExportValue::TYPE_STRING
                        ]
                    ]
                ]
            ]
        ]);

        // Add tables to records
        foreach ($exportData as $record) {
            $originalModel = $record->getMetadataValue('source_model');
            if ($originalModel) {
                $tableCollection = $tableProvider->getTablesForRecord($originalModel);
                $record->setTableCollection($tableCollection);
            }
        }

        // Export to Excel with tables
        $adapter = new ExcelExportFormatAdapter($registry, 'customers', 'export');
        $adapter->export('customer_export_with_tables.xlsx', 'xlsx', $exportData);
    }

    /**
     * Example 2: Manual table creation
     */
    public static function manualTableCreation(\Registry $registry)
    {
        // Create main export data
        $header = new ExportHeader();
        $header->addColumn(new ExportColumn('Customer Name', 'name', 'Customer Name'));
        $header->addColumn(new ExportColumn('Email', 'email', 'Email'));

        $exportData = new ExportData($header);

        // Create a main record
        $record = new ExportRecord();
        $record->addValue('name', 'John Doe', ExportValue::TYPE_STRING);
        $record->addValue('email', '<EMAIL>', ExportValue::TYPE_STRING);

        // Create purchase table manually
        $purchaseHeader = new ExportHeader();
        $purchaseHeader->addColumn(new ExportColumn('Item', 'item', 'Item'));
        $purchaseHeader->addColumn(new ExportColumn('Amount', 'amount', 'Amount'));
        $purchaseHeader->addColumn(new ExportColumn('Date', 'date', 'Date'));

        $purchaseTable = new ExportTable('purchases', 'Purchase History', $purchaseHeader, 'customer_1');

        // Add purchase records
        $purchase1 = new ExportRecord();
        $purchase1->addValue('item', 'Laptop', ExportValue::TYPE_STRING);
        $purchase1->addValue('amount', 1299.99, ExportValue::TYPE_FLOAT, '2');
        $purchase1->addValue('date', new \DateTime('2024-01-15'), ExportValue::TYPE_DATE);
        $purchaseTable->addRecord($purchase1);

        $purchase2 = new ExportRecord();
        $purchase2->addValue('item', 'Mouse', ExportValue::TYPE_STRING);
        $purchase2->addValue('amount', 29.99, ExportValue::TYPE_FLOAT, '2');
        $purchase2->addValue('date', new \DateTime('2024-01-20'), ExportValue::TYPE_DATE);
        $purchaseTable->addRecord($purchase2);

        // Create table collection and add to record
        $tableCollection = new ExportTableCollection();
        $tableCollection->addTable($purchaseTable);
        $record->setTableCollection($tableCollection);

        // Add record to export data
        $exportData->addRecord($record);

        // Enable tables and export
        $exportData->enableTables();

        $adapter = new ExcelExportFormatAdapter($registry, 'customers', 'export');
        $adapter->export('manual_table_export.xlsx', 'xlsx', $exportData);
    }

    /**
     * Example 3: Conditional table inclusion
     */
    public static function conditionalTableExport(\Registry $registry, array $models, \Outlook $outlook)
    {
        $dataFactory = new \Nzoom\Export\DataFactory($registry);
        $exportData = $dataFactory($models, $outlook);

        // Enable tables only if requested
        $includeDetailedData = $_GET['include_details'] ?? false;
        if ($includeDetailedData) {
            $exportData->enableTables([
                'table_types' => ['purchases', 'notes'],
                'max_records_per_table' => 100
            ]);

            $tableProvider = new ModelTableProvider($registry, [
                'table_types' => [
                    'purchases' => [
                        'name' => 'Recent Purchases',
                        'relation_method' => 'getRecentPurchases',
                        'columns' => [
                            [
                                'var_name' => 'product',
                                'label' => 'Product',
                                'field' => 'product_name',
                                'type' => ExportValue::TYPE_STRING
                            ],
                            [
                                'var_name' => 'total',
                                'label' => 'Total',
                                'field' => 'total_amount',
                                'type' => ExportValue::TYPE_FLOAT,
                                'format' => '2'
                            ]
                        ]
                    ],
                    'notes' => [
                        'name' => 'Customer Notes',
                        'relation_method' => 'getNotes',
                        'columns' => [
                            [
                                'var_name' => 'note_text',
                                'label' => 'Note',
                                'field' => 'text',
                                'type' => ExportValue::TYPE_STRING
                            ],
                            [
                                'var_name' => 'created',
                                'label' => 'Created',
                                'field' => 'created_at',
                                'type' => ExportValue::TYPE_DATETIME
                            ]
                        ]
                    ]
                ]
            ]);

            // Add tables to records
            foreach ($exportData as $record) {
                $originalModel = $record->getMetadataValue('source_model');
                if ($originalModel) {
                    $tableCollection = $tableProvider->getTablesForRecord($originalModel);
                    $record->setTableCollection($tableCollection);
                }
            }
        }

        $adapter = new ExcelExportFormatAdapter($registry, 'customers', 'export');
        $filename = $includeDetailedData ? 'detailed_export.xlsx' : 'basic_export.xlsx';
        $adapter->export($filename, 'xlsx', $exportData);
    }

    /**
     * Example 4: Table validation and error handling
     */
    public static function tableValidationExample(\Registry $registry, array $models)
    {
        $tableProvider = new ModelTableProvider($registry, [
            'table_types' => [
                'orders' => [
                    'name' => 'Order History',
                    'relation_method' => 'getOrders',
                    'columns' => [
                        [
                            'var_name' => 'order_id',
                            'label' => 'Order ID',
                            'field' => 'id',
                            'type' => ExportValue::TYPE_INTEGER
                        ]
                    ]
                ]
            ]
        ]);

        $validModels = [];
        $invalidModels = [];

        // Validate models before processing
        foreach ($models as $model) {
            if ($tableProvider->validateRecord($model, ['orders'])) {
                $validModels[] = $model;
            } else {
                $invalidModels[] = $model;
            }
        }

        if (!empty($invalidModels)) {
            if (isset($registry['logger'])) {
                $registry['logger']->warning(
                    'Some models cannot provide required table data: ' . count($invalidModels) . ' models excluded'
                );
            }
        }

        // Process only valid models
        if (!empty($validModels)) {
            $dataFactory = new \Nzoom\Export\DataFactory($registry);
            $outlook = new \Outlook(); // Create appropriate outlook
            $exportData = $dataFactory($validModels, $outlook);
            $exportData->enableTables(['table_types' => ['orders']]);

            // Add tables and export...
        }
    }
}
