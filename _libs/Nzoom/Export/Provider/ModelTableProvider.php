<?php

namespace Nzoom\Export\Provider;

use Nzoom\Export\Entity\ExportTable;
use Nzoom\Export\Entity\ExportTableCollection;
use Nzoom\Export\Entity\ExportHeader;
use Nzoom\Export\Entity\ExportColumn;
use Nzoom\Export\Entity\ExportRecord;
use Nzoom\Export\Entity\ExportValue;

/**
 * Class ModelTableProvider
 *
 * Example implementation of ExportTableProviderInterface for Model objects
 * Extracts related table data from Model instances based on configuration
 */
class ModelTableProvider implements ExportTableProviderInterface
{
    /**
     * @var array Configuration for table extraction
     */
    private $config;

    /**
     * @var \Registry
     */
    private $registry;

    /**
     * ModelTableProvider constructor
     *
     * @param \Registry $registry
     * @param array $config Configuration for table extraction
     */
    public function __construct(\Registry $registry, array $config = [])
    {
        $this->registry = $registry;
        $this->config = array_merge($this->getDefaultConfig(), $config);
    }

    /**
     * Get default configuration
     *
     * @return array
     */
    private function getDefaultConfig(): array
    {
        return [
            'table_types' => [],
            'max_records_per_table' => 1000,
            'include_empty_tables' => false,
            'date_format' => 'd.m.Y',
            'datetime_format' => 'd.m.Y H:i',
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function getTablesForRecord($record, array $options = []): ExportTableCollection
    {
        $collection = new ExportTableCollection();
        $mergedOptions = array_merge($this->config, $options);

        if (!($record instanceof \Model)) {
            return $collection;
        }

        $requestedTableTypes = $mergedOptions['table_types'] ?? [];
        if (empty($requestedTableTypes)) {
            return $collection;
        }

        foreach ($requestedTableTypes as $tableType) {
            if (!$this->supportsTableType($tableType)) {
                continue;
            }

            $table = $this->extractTable($record, $tableType, $mergedOptions);
            if ($table && ($table->hasRecords() || $mergedOptions['include_empty_tables'])) {
                $collection->addTable($table);
            }
        }

        return $collection;
    }

    /**
     * Extract a specific table from a record
     *
     * @param \Model $record
     * @param string $tableType
     * @param array $options
     * @return ExportTable|null
     */
    private function extractTable(\Model $record, string $tableType, array $options): ?ExportTable
    {
        $tableConfig = $this->getTableConfiguration($tableType);
        if (empty($tableConfig)) {
            return null;
        }

        $tableName = $tableConfig['name'] ?? $tableType;
        $relationMethod = $tableConfig['relation_method'] ?? null;
        $columns = $tableConfig['columns'] ?? [];

        if (!$relationMethod || empty($columns)) {
            return null;
        }

        // Create header
        $header = $this->createTableHeader($columns);

        // Create table
        $table = new ExportTable(
            $tableType,
            $tableName,
            $header,
            $record->get('id'),
            ['source_model' => get_class($record)]
        );

        // Extract related data
        try {
            if (method_exists($record, $relationMethod)) {
                $relatedData = $record->$relationMethod();
                $this->populateTableFromData($table, $relatedData, $columns, $options);
            }
        } catch (\Exception $e) {
            // Log error but continue
            if (isset($this->registry['logger'])) {
                $this->registry['logger']->warning(
                    "Failed to extract table '{$tableType}' from record: " . $e->getMessage()
                );
            }
        }

        return $table;
    }

    /**
     * Create table header from column configuration
     *
     * @param array $columns
     * @return ExportHeader
     */
    private function createTableHeader(array $columns): ExportHeader
    {
        $header = new ExportHeader();

        foreach ($columns as $columnConfig) {
            $varName = $columnConfig['var_name'] ?? '';
            $label = $columnConfig['label'] ?? $varName;
            $type = $columnConfig['type'] ?? 'string';

            if (empty($varName)) {
                continue;
            }

            $column = new ExportColumn($label, $varName, $label);
            $column->setType($type);

            if (isset($columnConfig['format'])) {
                $column->setFormat($columnConfig['format']);
            }

            $header->addColumn($column);
        }

        return $header;
    }

    /**
     * Populate table with data
     *
     * @param ExportTable $table
     * @param mixed $data
     * @param array $columns
     * @param array $options
     */
    private function populateTableFromData(ExportTable $table, $data, array $columns, array $options): void
    {
        $maxRecords = $options['max_records_per_table'] ?? 1000;
        $recordCount = 0;

        // Handle different data types
        if (is_array($data)) {
            foreach ($data as $item) {
                if ($recordCount >= $maxRecords) {
                    break;
                }

                $record = $this->createRecordFromItem($item, $columns, $options);
                if ($record) {
                    $table->addRecord($record, false);
                    $recordCount++;
                }
            }
        } elseif ($data instanceof \Model) {
            $record = $this->createRecordFromItem($data, $columns, $options);
            if ($record) {
                $table->addRecord($record, false);
            }
        }
    }

    /**
     * Create export record from data item
     *
     * @param mixed $item
     * @param array $columns
     * @param array $options
     * @return ExportRecord|null
     */
    private function createRecordFromItem($item, array $columns, array $options): ?ExportRecord
    {
        $record = new ExportRecord();

        foreach ($columns as $columnConfig) {
            $varName = $columnConfig['var_name'] ?? '';
            $field = $columnConfig['field'] ?? $varName;
            $type = $columnConfig['type'] ?? 'string';
            $format = $columnConfig['format'] ?? null;

            if (empty($varName)) {
                continue;
            }

            $value = $this->extractValueFromItem($item, $field);
            $formattedValue = $this->formatValue($value, $type, $format, $options);

            $record->addValue($varName, $formattedValue, $type, $format);
        }

        return $record;
    }

    /**
     * Extract value from data item
     *
     * @param mixed $item
     * @param string $field
     * @return mixed
     */
    private function extractValueFromItem($item, string $field)
    {
        if ($item instanceof \Model) {
            return $item->get($field);
        } elseif (is_array($item)) {
            return $item[$field] ?? null;
        } elseif (is_object($item) && property_exists($item, $field)) {
            return $item->$field;
        }

        return null;
    }

    /**
     * Format value based on type
     *
     * @param mixed $value
     * @param string $type
     * @param string|null $format
     * @param array $options
     * @return mixed
     */
    private function formatValue($value, string $type, ?string $format, array $options)
    {
        if ($value === null) {
            return null;
        }

        switch ($type) {
            case ExportValue::TYPE_DATE:
                if ($value instanceof \DateTimeInterface) {
                    return $value;
                } elseif (is_string($value) && strtotime($value) !== false) {
                    return new \DateTime($value);
                }
                break;

            case ExportValue::TYPE_DATETIME:
                if ($value instanceof \DateTimeInterface) {
                    return $value;
                } elseif (is_string($value) && strtotime($value) !== false) {
                    return new \DateTime($value);
                }
                break;

            case ExportValue::TYPE_INTEGER:
                return (int) $value;

            case ExportValue::TYPE_FLOAT:
                return (float) $value;

            case ExportValue::TYPE_BOOLEAN:
                return (bool) $value;
        }

        return $value;
    }

    /**
     * {@inheritdoc}
     */
    public function getSupportedTableTypes(): array
    {
        return array_keys($this->config['table_types'] ?? []);
    }

    /**
     * {@inheritdoc}
     */
    public function supportsTableType(string $tableType): bool
    {
        return isset($this->config['table_types'][$tableType]);
    }

    /**
     * {@inheritdoc}
     */
    public function getTableConfiguration(string $tableType): array
    {
        return $this->config['table_types'][$tableType] ?? [];
    }

    /**
     * {@inheritdoc}
     */
    public function validateRecord($record, array $requestedTableTypes = []): bool
    {
        if (!($record instanceof \Model)) {
            return false;
        }

        if (empty($requestedTableTypes)) {
            return true;
        }

        foreach ($requestedTableTypes as $tableType) {
            if (!$this->supportsTableType($tableType)) {
                return false;
            }

            $tableConfig = $this->getTableConfiguration($tableType);
            $relationMethod = $tableConfig['relation_method'] ?? null;

            if ($relationMethod && !method_exists($record, $relationMethod)) {
                return false;
            }
        }

        return true;
    }
}
